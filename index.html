<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>中文词语手写测试</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@3.2.47/dist/vue.global.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@babel/standalone/babel.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/tesseract.js@6.0.1/dist/tesseract.min.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px;
    }

    .app-container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 600px;
    }

    .title {
      font-size: 28px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 30px;
      color: #333;
    }

    .word-info {
      margin-bottom: 25px;
      text-align: center;
    }

    .pinyin {
      font-size: 20px;
      color: #666;
      margin-bottom: 15px;
    }

    .play-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s;
    }

    .play-btn:hover {
      background: #0056b3;
    }

    .canvas-container {
      position: relative;
      display: flex;
      justify-content: center;
      margin: 25px 0;
      background: #fafafa;
      border-radius: 8px;
      padding: 20px;
    }

    .writing-canvas {
      background: white;
      cursor: crosshair;
      border-radius: 4px;
    }

    .loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .button-group {
      display: flex;
      justify-content: space-between;
      gap: 15px;
      margin-bottom: 25px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      flex: 1;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .btn-clear {
      background: #6c757d;
      color: white;
    }

    .btn-clear:hover:not(:disabled) {
      background: #545b62;
    }

    .btn-submit {
      background: #28a745;
      color: white;
    }

    .btn-submit:hover:not(:disabled) {
      background: #1e7e34;
    }

    .btn-undo {
      background: #ffc107;
      color: #212529;
    }

    .btn-undo:hover:not(:disabled) {
      background: #e0a800;
    }

    .btn-check {
      background: #17a2b8;
      color: white;
    }

    .btn-check:hover:not(:disabled) {
      background: #138496;
    }

    .grid-status {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 15px 0;
      flex-wrap: wrap;
    }

    .char-status {
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: bold;
      min-width: 60px;
      text-align: center;
    }

    .char-status.pending {
      background: #f8f9fa;
      color: #6c757d;
      border: 2px solid #dee2e6;
    }

    .char-status.correct {
      background: #d4edda;
      color: #155724;
      border: 2px solid #c3e6cb;
    }

    .char-status.incorrect {
      background: #f8d7da;
      color: #721c24;
      border: 2px solid #f5c6cb;
    }

    .char-status.current {
      background: #fff3cd;
      color: #856404;
      border: 2px solid #ffeaa7;
    }

    .debug-panel {
      margin: 20px 0;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #dee2e6;
    }

    .debug-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #495057;
    }

    .debug-images {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .debug-char {
      text-align: center;
    }

    .debug-char canvas {
      border: 1px solid #ccc;
      background: white;
    }

    .debug-char-label {
      font-size: 12px;
      margin-top: 5px;
      color: #666;
    }

    .btn-debug {
      background: #6f42c1;
      color: white;
    }

    .btn-debug:hover:not(:disabled) {
      background: #5a32a3;
    }

    .btn-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .spinner {
      border: 2px solid #f3f3f3;
      border-top: 2px solid #ffffff;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .recognition-status {
      min-height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
    }

    .feedback {
      text-align: center;
      font-size: 18px;
      font-weight: bold;
    }

    .feedback.correct {
      color: #28a745;
    }

    .feedback.incorrect {
      color: #dc3545;
    }

    .recognized-text {
      text-align: center;
      color: #666;
      font-size: 16px;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div id="app" class="app-container">
    <h1 class="title">中文词语手写测试</h1>

    <div class="word-info">
      <p class="pinyin">拼音: {{ currentWord.pinyin }}</p>
      <button @click="playPronunciation" class="play-btn">
        播放发音
      </button>
    </div>

    <!-- 字符状态显示 -->
    <div class="grid-status">
      <div
        v-for="(char, index) in currentWord.text"
        :key="index"
        class="char-status"
        :class="getCharStatusClass(index)"
      >
        {{ char }}
        <div style="font-size: 12px; margin-top: 2px;">
          {{ getCharStatusText(index) }}
        </div>
      </div>
    </div>

    <div class="canvas-container" :class="{ loading: isRecognizing }">
      <canvas
        ref="canvas"
        :width="canvasWidth"
        :height="canvasHeight"
        class="writing-canvas"
        @mousedown="startDrawing"
        @mousemove="draw"
        @mouseup="stopDrawing"
        @mouseleave="stopDrawing"
        @touchstart.prevent="startDrawing"
        @touchmove.prevent="draw"
        @touchend.prevent="stopDrawing"
        @touchcancel.prevent="stopDrawing"
      ></canvas>
    </div>

    <div class="button-group">
      <button
        @click="clearCanvas"
        :disabled="isRecognizing"
        class="btn btn-clear"
      >
        清除画布
      </button>
      <button
        @click="undoLastStroke"
        :disabled="isRecognizing || strokeHistory.length === 0"
        class="btn btn-undo"
      >
        撤销
      </button>
      <button
        @click="showDebugImages"
        :disabled="isRecognizing"
        class="btn btn-debug"
      >
        查看提取图像
      </button>
      <button
        @click="recognizeAllChars"
        :disabled="isRecognizing"
        class="btn btn-submit"
      >
        <div class="btn-content">
          <span v-if="!isRecognizing">提交识别</span>
          <span v-else>
            <div class="spinner"></div>
            识别中...
          </span>
        </div>
      </button>
    </div>

    <div class="button-group" v-if="allCharsCompleted">
      <button
        @click="nextWord"
        class="btn btn-check"
        style="width: 100%;"
      >
        下一个词语
      </button>
    </div>

    <div class="recognition-status">
      <p v-if="feedback" class="feedback" :class="feedback.isCorrect ? 'correct' : 'incorrect'">
        {{ feedback.message }}
      </p>
    </div>

    <p v-if="recognizedText && !isRecognizing" class="recognized-text">
      识别结果: {{ recognizedText }}
    </p>

    <!-- 调试面板 -->
    <div v-if="debugImages.length > 0" class="debug-panel">
      <div class="debug-title">提取的字符图像（用于OCR识别）</div>
      <div class="debug-images">
        <div v-for="(debugImg, index) in debugImages" :key="index" class="debug-char">
          <canvas
            :ref="`debugCanvas${index}`"
            :width="debugImg.width"
            :height="debugImg.height"
            style="width: 80px; height: 80px;"
          ></canvas>
          <div class="debug-char-label">
            字符{{ index + 1 }}: {{ currentWord.text[index] }}
            <br>
            <span v-if="charResults[index]" :style="{ color: charResults[index].isCorrect ? '#28a745' : '#dc3545' }">
              识别: {{ charResults[index].recognized || '无' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script type="text/babel">
    const { createApp } = Vue;

    const app = createApp({
      data() {
        return {
          words: [
            { text: '你好好', pinyin: 'nǐ hǎo hǎo', audio: 'https://dict.youdao.com/dictvoice?audio=nihao&type=1' },
            { text: '谢谢', pinyin: 'xiè xie', audio: 'https://dict.youdao.com/dictvoice?audio=xiexie&type=1' },
            { text: '再见', pinyin: 'zài jiàn', audio: 'https://dict.youdao.com/dictvoice?audio=zaijian&type=1' },
            { text: '朋友', pinyin: 'péng yǒu', audio: 'https://dict.youdao.com/dictvoice?audio=pengyou&type=1' },
            { text: '家人', pinyin: 'jiā rén', audio: 'https://dict.youdao.com/dictvoice?audio=jiaren&type=1' },
            { text: '学习', pinyin: 'xué xí', audio: 'https://dict.youdao.com/dictvoice?audio=xuexi&type=1' },
            { text: '工作', pinyin: 'gōng zuò', audio: 'https://dict.youdao.com/dictvoice?audio=gongzuo&type=1' },
            { text: '时间', pinyin: 'shí jiān', audio: 'https://dict.youdao.com/dictvoice?audio=shijian&type=1' }
          ],
          currentIndex: 0,
          recognizedText: '',
          feedback: null,
          isDrawing: false,
          isRecognizing: false,
          ctx: null,
          gridSize: 120,
          spacing: 15,
          lastX: 0,
          lastY: 0,
          strokeHistory: [], // 存储每一笔的画布状态
          currentStroke: [],  // 当前正在绘制的笔画
          charResults: [], // 每个字符的识别结果
          recognitionProgress: {}, // 识别进度状态
          debugImages: [] // 调试用的提取图像
        };
      },
      computed: {
        currentWord() {
          return this.words[this.currentIndex];
        },
        canvasWidth() {
          return this.currentWord.text.length * this.gridSize + (this.currentWord.text.length - 1) * this.spacing;
        },
        canvasHeight() {
          return this.gridSize;
        },
        allCharsCompleted() {
          return this.charResults.length === this.currentWord.text.length &&
                 this.charResults.every(result => result && result.isCorrect);
        }
      },
      mounted() {
        this.ctx = this.$refs.canvas.getContext('2d');

        // 设置画笔属性
        this.ctx.lineWidth = 3;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.strokeStyle = '#000000';
        this.ctx.fillStyle = '#000000';

        // 设置高DPI支持
        this.setupHighDPI();
        this.drawTianziGrid();
      },
      watch: {
        currentWord() {
          this.resetWordState();
        }
      },
      methods: {
        setupHighDPI() {
          const canvas = this.$refs.canvas;
          const dpr = window.devicePixelRatio || 1;

          // 设置实际大小
          canvas.width = this.canvasWidth * dpr;
          canvas.height = this.canvasHeight * dpr;

          // 设置显示大小
          canvas.style.width = this.canvasWidth + 'px';
          canvas.style.height = this.canvasHeight + 'px';

          // 缩放上下文
          this.ctx.scale(dpr, dpr);
        },

        drawTianziGrid() {
          // 先清除画布
          this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

          // 保存当前绘图状态
          this.ctx.save();

          // 绘制田字格作为背景
          for (let i = 0; i < this.currentWord.text.length; i++) {
            const x = i * (this.gridSize + this.spacing);

            // 外框 - 使用较粗的线条
            this.ctx.strokeStyle = '#999999';
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(x, 0, this.gridSize, this.gridSize);

            // 内部辅助线 - 使用细线
            this.ctx.strokeStyle = '#dddddd';
            this.ctx.lineWidth = 1;

            // 垂直中线
            this.ctx.beginPath();
            this.ctx.moveTo(x + this.gridSize / 2, 0);
            this.ctx.lineTo(x + this.gridSize / 2, this.gridSize);
            this.ctx.stroke();

            // 水平中线
            this.ctx.beginPath();
            this.ctx.moveTo(x, this.gridSize / 2);
            this.ctx.lineTo(x + this.gridSize, this.gridSize / 2);
            this.ctx.stroke();

            // 对角线 - 使用更淡的颜色
            this.ctx.strokeStyle = '#eeeeee';
            this.ctx.lineWidth = 0.5;

            // 左上到右下对角线
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x + this.gridSize, this.gridSize);
            this.ctx.stroke();

            // 右上到左下对角线
            this.ctx.beginPath();
            this.ctx.moveTo(x + this.gridSize, 0);
            this.ctx.lineTo(x, this.gridSize);
            this.ctx.stroke();
          }

          // 恢复绘图状态，为手写做准备
          this.ctx.restore();
          this.ctx.lineWidth = 3;
          this.ctx.lineCap = 'round';
          this.ctx.lineJoin = 'round';
          this.ctx.strokeStyle = '#000000';
          this.ctx.fillStyle = '#000000';
        },

        saveCanvasState() {
          // 保存当前画布状态到历史记录
          const imageData = this.ctx.getImageData(0, 0, this.$refs.canvas.width, this.$refs.canvas.height);
          this.strokeHistory.push(imageData);

          // 限制历史记录数量，避免内存过多占用
          if (this.strokeHistory.length > 20) {
            this.strokeHistory.shift();
          }
        },
        playPronunciation() {
          const audio = new Audio(this.currentWord.audio);
          audio.play();
        },
        getEventPos(e) {
          const rect = this.$refs.canvas.getBoundingClientRect();
          const clientX = e.clientX || (e.touches && e.touches[0].clientX);
          const clientY = e.clientY || (e.touches && e.touches[0].clientY);
          return {
            x: (clientX - rect.left) * (this.$refs.canvas.width / rect.width) / (window.devicePixelRatio || 1),
            y: (clientY - rect.top) * (this.$refs.canvas.height / rect.height) / (window.devicePixelRatio || 1)
          };
        },

        isInAnyGrid(x, y) {
          // 检查坐标是否在任何格子内（不在间隔区域）
          for (let i = 0; i < this.currentWord.text.length; i++) {
            const gridX = i * (this.gridSize + this.spacing);
            if (x >= gridX && x <= gridX + this.gridSize && y >= 0 && y <= this.gridSize) {
              return true;
            }
          }
          return false;
        },

        startDrawing(e) {
          if (this.isRecognizing) return;

          const pos = this.getEventPos(e);

          // 检查是否在任何格子内（不在间隔区域）
          if (!this.isInAnyGrid(pos.x, pos.y)) return;

          // 在开始新的笔画前保存当前状态
          this.saveCanvasState();

          this.isDrawing = true;
          this.lastX = pos.x;
          this.lastY = pos.y;
          this.currentStroke = [pos];
          this.ctx.beginPath();
          this.ctx.moveTo(pos.x, pos.y);
        },

        draw(e) {
          if (!this.isDrawing || this.isRecognizing) return;
          const pos = this.getEventPos(e);

          // 检查是否在任何格子内
          if (!this.isInAnyGrid(pos.x, pos.y)) {
            this.stopDrawing();
            return;
          }

          // 记录当前笔画的点
          this.currentStroke.push(pos);

          // 使用二次贝塞尔曲线使线条更平滑
          this.ctx.quadraticCurveTo(this.lastX, this.lastY, (pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2);
          this.ctx.stroke();
          this.ctx.beginPath();
          this.ctx.moveTo((pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2);

          this.lastX = pos.x;
          this.lastY = pos.y;
        },

        stopDrawing() {
          if (this.isDrawing) {
            this.isDrawing = false;
            this.ctx.closePath();
            this.currentStroke = []; // 清空当前笔画
          }
        },
        resetWordState() {
          this.charResults = [];
          this.recognitionProgress = {};
          this.debugImages = [];
          this.clearCanvas();
        },

        clearCanvas() {
          this.drawTianziGrid(); // 清除后重新绘制田字格
          this.strokeHistory = []; // 清空撤销历史
          this.currentStroke = [];
          this.recognizedText = '';
          this.feedback = null;
        },



        drawSingleGrid(index) {
          const x = index * (this.gridSize + this.spacing);

          // 保存当前绘图状态
          this.ctx.save();

          // 外框
          this.ctx.strokeStyle = '#999999';
          this.ctx.lineWidth = 2;
          this.ctx.strokeRect(x, 0, this.gridSize, this.gridSize);

          // 内部辅助线
          this.ctx.strokeStyle = '#dddddd';
          this.ctx.lineWidth = 1;

          // 垂直中线
          this.ctx.beginPath();
          this.ctx.moveTo(x + this.gridSize / 2, 0);
          this.ctx.lineTo(x + this.gridSize / 2, this.gridSize);
          this.ctx.stroke();

          // 水平中线
          this.ctx.beginPath();
          this.ctx.moveTo(x, this.gridSize / 2);
          this.ctx.lineTo(x + this.gridSize, this.gridSize / 2);
          this.ctx.stroke();

          // 对角线
          this.ctx.strokeStyle = '#eeeeee';
          this.ctx.lineWidth = 0.5;

          this.ctx.beginPath();
          this.ctx.moveTo(x, 0);
          this.ctx.lineTo(x + this.gridSize, this.gridSize);
          this.ctx.stroke();

          this.ctx.beginPath();
          this.ctx.moveTo(x + this.gridSize, 0);
          this.ctx.lineTo(x, this.gridSize);
          this.ctx.stroke();

          // 恢复绘图状态
          this.ctx.restore();
          this.ctx.lineWidth = 3;
          this.ctx.lineCap = 'round';
          this.ctx.lineJoin = 'round';
          this.ctx.strokeStyle = '#000000';
          this.ctx.fillStyle = '#000000';
        },

        undoLastStroke() {
          if (this.strokeHistory.length === 0) return;

          // 恢复到上一个状态
          const lastState = this.strokeHistory.pop();
          this.ctx.putImageData(lastState, 0, 0);

          this.recognizedText = '';
          this.feedback = null;
        },

        getCharStatusClass(index) {
          if (this.recognitionProgress[index] === 'recognizing') return 'current';
          if (this.charResults[index]) {
            return this.charResults[index].isCorrect ? 'correct' : 'incorrect';
          }
          return 'pending';
        },

        getCharStatusText(index) {
          if (this.recognitionProgress[index] === 'recognizing') return '识别中';
          if (this.charResults[index]) {
            return this.charResults[index].isCorrect ? '正确' : '错误';
          }
          return '待识别';
        },

        showDebugImages() {
          // 提取所有字符的图像用于调试
          this.debugImages = [];

          for (let i = 0; i < this.currentWord.text.length; i++) {
            const charCanvas = this.extractCharCanvasAtIndex(i);

            // 保存图像数据
            this.debugImages.push({
              canvas: charCanvas,
              width: charCanvas.width,
              height: charCanvas.height,
              imageData: charCanvas.getContext('2d').getImageData(0, 0, charCanvas.width, charCanvas.height)
            });
          }

          // 等待Vue更新DOM后绘制调试图像
          this.$nextTick(() => {
            this.renderDebugImages();
          });
        },

        renderDebugImages() {
          this.debugImages.forEach((debugImg, index) => {
            const canvasRef = this.$refs[`debugCanvas${index}`];
            if (canvasRef && canvasRef[0]) {
              const canvas = canvasRef[0];
              const ctx = canvas.getContext('2d');

              // 清除画布
              ctx.clearRect(0, 0, canvas.width, canvas.height);

              // 绘制提取的图像
              ctx.putImageData(debugImg.imageData, 0, 0);
            }
          });
        },

        async recognizeAllChars() {
          if (this.isRecognizing) return;

          this.isRecognizing = true;
          this.recognizedText = '';
          this.feedback = null;
          this.charResults = [];
          this.recognitionProgress = {};

          try {
            // 创建所有字符的识别任务
            const recognitionTasks = [];

            for (let i = 0; i < this.currentWord.text.length; i++) {
              this.recognitionProgress[i] = 'recognizing';
              recognitionTasks.push(this.recognizeCharAtIndex(i));
            }

            // 并行执行所有识别任务
            const results = await Promise.allSettled(recognitionTasks);

            // 处理结果
            let allCorrect = true;
            let resultMessage = '';

            results.forEach((result, index) => {
              this.recognitionProgress[index] = 'completed';

              if (result.status === 'fulfilled') {
                this.charResults[index] = result.value;
                if (!result.value.isCorrect) {
                  allCorrect = false;
                }
              } else {
                this.charResults[index] = {
                  recognized: '',
                  expected: this.currentWord.text[index],
                  isCorrect: false
                };
                allCorrect = false;
              }
            });

            // 生成反馈信息
            const correctCount = this.charResults.filter(r => r.isCorrect).length;
            const totalCount = this.charResults.length;

            if (allCorrect) {
              resultMessage = `全部正确！识别结果：${this.charResults.map(r => r.recognized).join('')}`;
            } else {
              resultMessage = `识别完成：${correctCount}/${totalCount} 正确`;
            }

            this.feedback = {
              isCorrect: allCorrect,
              message: resultMessage
            };

            if (allCorrect) {
              setTimeout(() => {
                this.feedback = {
                  isCorrect: true,
                  message: '恭喜！可以进入下一个词语了'
                };
              }, 2000);
            }

          } catch (error) {
            console.error('识别失败:', error);
            this.feedback = {
              isCorrect: false,
              message: '识别失败，请重试'
            };
          } finally {
            this.isRecognizing = false;
          }
        },

        async recognizeCharAtIndex(index) {
          try {
            // 创建只包含指定格子的canvas
            const charCanvas = this.extractCharCanvasAtIndex(index);

            const result = await Tesseract.recognize(
              charCanvas,
              'chi_sim',
              {
                logger: (m) => {
                  if (m.status === 'recognizing text') {
                    console.log(`字符${index + 1}识别进度: ${Math.round(m.progress * 100)}%`);
                  }
                },
                tessedit_pageseg_mode: Tesseract.PSM.SINGLE_CHAR,
                tessedit_char_whitelist: this.currentWord.text,
                preserve_interword_spaces: '0'
              }
            );

            let recognizedText = result.data.text.trim();

            // 清理识别结果，只保留中文
            recognizedText = recognizedText.replace(/[^\u4e00-\u9fa5]/g, '');

            // 检查字符
            const expectedChar = this.currentWord.text[index];
            const isCorrect = recognizedText === expectedChar;

            return {
              recognized: recognizedText,
              expected: expectedChar,
              isCorrect: isCorrect
            };

          } catch (error) {
            console.error(`字符${index + 1}识别失败:`, error);
            throw error;
          }
        },

        extractCharCanvasAtIndex(index) {
          // 创建临时canvas，只包含指定字符格子的内容
          const tempCanvas = document.createElement('canvas');
          const tempCtx = tempCanvas.getContext('2d');

          // 设置canvas大小为单个格子大小
          tempCanvas.width = this.gridSize;
          tempCanvas.height = this.gridSize;

          // 填充白色背景
          tempCtx.fillStyle = 'white';
          tempCtx.fillRect(0, 0, this.gridSize, this.gridSize);

          // 计算指定格子在原canvas中的位置
          const sourceX = index * (this.gridSize + this.spacing);

          // 从原canvas中提取指定格子的内容
          tempCtx.drawImage(
            this.$refs.canvas,
            sourceX, 0, this.gridSize, this.gridSize,  // 源区域
            0, 0, this.gridSize, this.gridSize         // 目标区域
          );

          return tempCanvas;
        },

        nextWord() {
          this.currentIndex = (this.currentIndex + 1) % this.words.length;
        },

        preprocessCanvas() {
          // 创建一个临时canvas用于预处理
          const tempCanvas = document.createElement('canvas');
          const tempCtx = tempCanvas.getContext('2d');

          // 设置临时canvas大小
          tempCanvas.width = this.$refs.canvas.width;
          tempCanvas.height = this.$refs.canvas.height;

          // 填充白色背景
          tempCtx.fillStyle = 'white';
          tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

          // 绘制原始内容
          tempCtx.drawImage(this.$refs.canvas, 0, 0);

          // 获取图像数据进行处理
          const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
          const data = imageData.data;

          // 二值化处理 - 增强对比度
          for (let i = 0; i < data.length; i += 4) {
            const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
            const binary = gray < 128 ? 0 : 255;
            data[i] = binary;     // R
            data[i + 1] = binary; // G
            data[i + 2] = binary; // B
          }

          tempCtx.putImageData(imageData, 0, 0);
          return tempCanvas;
        },

        async recognizeAndCheck() {
          if (this.isRecognizing) return;

          this.isRecognizing = true;
          this.recognizedText = '';
          this.feedback = null;

          try {
            // 预处理图像
            const processedCanvas = this.preprocessCanvas();

            const result = await Tesseract.recognize(
              processedCanvas,
              'chi_sim',
              {
                logger: (m) => {
                  if (m.status === 'recognizing text') {
                    console.log(`识别进度: ${Math.round(m.progress * 100)}%`);
                  }
                },
                tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
                tessedit_char_whitelist: '你好谢再见朋友家人学习工作时间',
                preserve_interword_spaces: '0'
              }
            );

            let recognizedText = result.data.text.trim();

            // 清理识别结果
            recognizedText = recognizedText.replace(/[^\u4e00-\u9fa5]/g, '');

            this.recognizedText = recognizedText;

            // 检查答案 - 支持部分匹配
            const correct = this.checkAnswer(recognizedText, this.currentWord.text);

            this.feedback = {
              isCorrect: correct,
              message: correct ? '正确！' : `错误！正确答案是：${this.currentWord.text}`
            };

            if (correct) {
              setTimeout(() => {
                this.currentIndex = (this.currentIndex + 1) % this.words.length;
                this.recognizedText = '';
                this.feedback = null;
                this.clearCanvas();
              }, 2000);
            }
          } catch (error) {
            console.error('识别失败:', error);
            this.feedback = {
              isCorrect: false,
              message: '识别失败，请重试'
            };
          } finally {
            this.isRecognizing = false;
          }
        },

        checkAnswer(recognized, correct) {
          // 完全匹配
          if (recognized === correct) return true;

          // 计算相似度
          const similarity = this.calculateSimilarity(recognized, correct);
          return similarity >= 0.8; // 80%相似度认为正确
        },

        calculateSimilarity(str1, str2) {
          if (str1.length === 0 && str2.length === 0) return 1;
          if (str1.length === 0 || str2.length === 0) return 0;

          let matches = 0;
          const minLength = Math.min(str1.length, str2.length);

          for (let i = 0; i < minLength; i++) {
            if (str1[i] === str2[i]) matches++;
          }

          return matches / Math.max(str1.length, str2.length);
        }
      }
    });

    app.mount('#app');
  </script>
</body>
</html>