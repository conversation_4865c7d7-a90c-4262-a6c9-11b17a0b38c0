<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>中文词语手写测试</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@3.2.47/dist/vue.global.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@babel/standalone/babel.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/tesseract.js@6.0.1/dist/tesseract.min.js"></script>
  <style>
    .canvas-container {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
    }
    .tianzi-grid {
      background: white;
    }
    .grid-lines {
      position: absolute;
      top: 0;
      left: 0;
      pointer-events: none;
    }
  </style>
</head>
<body class="bg-gray-100 flex items-center justify-center h-screen">
  <div id="app" class="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg">
    <h1 class="text-2xl font-bold text-center mb-4">中文词语手写测试</h1>
    
    <div class="mb-4">
      <p class="text-lg">拼音: {{ currentWord.pinyin }}</p>
      <button 
        @click="playPronunciation" 
        class="mt-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        播放发音
      </button>
    </div>

    <div class="mb-4 canvas-container">
      <canvas 
        ref="canvas" 
        :width="canvasWidth" 
        :height="canvasHeight" 
        class="tianzi-grid border border-gray-300"
        @mousedown="startDrawing"
        @mousemove="draw"
        @mouseup="stopDrawing"
        @touchstart="startDrawing"
        @touchmove="draw"
        @touchend="stopDrawing"
      ></canvas>
      <canvas 
        ref="gridCanvas" 
        :width="canvasWidth" 
        :height="canvasHeight" 
        class="grid-lines"
      ></canvas>
    </div>
    <div class="flex justify-between mb-4">
      <button 
        @click="clearCanvas" 
        class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
      >
        清除画布
      </button>
      <button 
        @click="recognizeAndCheck" 
        class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
      >
        提交答案
      </button>
    </div>

    <p v-if="feedback" class="mt-4 text-center" :class="feedback.isCorrect ? 'text-green-500' : 'text-red-500'">
      {{ feedback.message }}
    </p>
    <p v-if="recognizedText" class="mt-2 text-center text-gray-600">
      识别结果: {{ recognizedText }}
    </p>
  </div>

  <script type="text/babel">
    const { createApp } = Vue;

    const app = createApp({
      data() {
        return {
          words: [
            { text: '你好', pinyin: 'nǐ hǎo', audio: 'https://dict.youdao.com/dictvoice?audio=nihao&type=1' },
            { text: '谢谢', pinyin: 'xiè xie', audio: 'https://dict.youdao.com/dictvoice?audio=xiexie&type=1' },
            { text: '再见', pinyin: 'zài jiàn', audio: 'https://dict.youdao.com/dictvoice?audio=zaijian&type=1' },
            { text: '朋友', pinyin: 'péng yǒu', audio: 'https://dict.youdao.com/dictvoice?audio=pengyou&type=1' },
            { text: '家人', pinyin: 'jiā rén', audio: 'https://dict.youdao.com/dictvoice?audio=jiaren&type=1' }
          ],
          currentIndex: 0,
          recognizedText: '',
          feedback: null,
          isDrawing: false,
          ctx: null,
          gridCtx: null,
          gridSize: 100,
          spacing: 20
        };
      },
      computed: {
        currentWord() {
          return this.words[this.currentIndex];
        },
        canvasWidth() {
          return this.currentWord.text.length * this.gridSize + (this.currentWord.text.length - 1) * this.spacing;
        },
        canvasHeight() {
          return this.gridSize;
        }
      },
      mounted() {
        this.ctx = this.$refs.canvas.getContext('2d');
        this.gridCtx = this.$refs.gridCanvas.getContext('2d');
        this.ctx.lineWidth = 2;
        this.ctx.lineCap = 'round';
        this.ctx.strokeStyle = 'black';
        this.drawGrid();
      },
      watch: {
        currentWord() {
          this.clearCanvas();
          this.drawGrid();
          this.recognizedText = '';
          this.feedback = null;
        }
      },
      methods: {
        drawGrid() {
          this.gridCtx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
          this.gridCtx.strokeStyle = 'lightgray';
          this.gridCtx.lineWidth = 1;
          const totalWidth = this.canvasWidth;
          const startX = 0; // 直接从左边开始，确保田字格对齐手写区域
          for (let i = 0; i < this.currentWord.text.length; i++) {
            const x = startX + i * (this.gridSize + this.spacing);
            // 外框
            this.gridCtx.strokeRect(x, 0, this.gridSize, this.gridSize);
            // 垂直中线
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(x + this.gridSize / 2, 0);
            this.gridCtx.lineTo(x + this.gridSize / 2, this.gridSize);
            this.gridCtx.stroke();
            // 水平中线
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(x, this.gridSize / 2);
            this.gridCtx.lineTo(x + this.gridSize, this.gridSize / 2);
            this.gridCtx.stroke();
            // 左上到右下对角线
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(x, 0);
            this.gridCtx.lineTo(x + this.gridSize, this.gridSize);
            this.gridCtx.stroke();
            // 右上到左下对角线
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(x + this.gridSize, 0);
            this.gridCtx.lineTo(x, this.gridSize);
            this.gridCtx.stroke();
          }
        },
        playPronunciation() {
          const audio = new Audio(this.currentWord.audio);
          audio.play();
        },
        startDrawing(e) {
          this.isDrawing = true;
          this.ctx.beginPath();
          const rect = this.$refs.canvas.getBoundingClientRect();
          const x = (e.clientX || e.touches[0].clientX) - rect.left;
          const y = (e.clientY || e.touches[0].clientY) - rect.top;
          this.ctx.moveTo(x, y);
        },
        draw(e) {
          if (!this.isDrawing) return;
          const rect = this.$refs.canvas.getBoundingClientRect();
          const x = (e.clientX || e.touches[0].clientX) - rect.left;
          const y = (e.clientY || e.touches[0].clientY) - rect.top;
          this.ctx.lineTo(x, y);
          this.ctx.stroke();
        },
        stopDrawing() {
          this.isDrawing = false;
        },
        clearCanvas() {
          this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
          this.recognizedText = '';
          this.feedback = null;
        },
        async recognizeAndCheck() {
          try {
            const result = await Tesseract.recognize(
              this.$refs.canvas,
              'chi_sim',
              {
                logger: (m) => console.log(m)
              }
            );
            this.recognizedText = result.data.text.trim();
            const correct = this.recognizedText === this.currentWord.text;
            this.feedback = {
              isCorrect: correct,
              message: correct ? '正确！' : `错误！正确答案是：${this.currentWord.text}`
            };
            if (correct) {
              setTimeout(() => {
                this.currentIndex = (this.currentIndex + 1) % this.words.length;
                this.recognizedText = '';
                this.feedback = null;
                this.clearCanvas();
              }, 1000);
            }
          } catch (error) {
            console.error('识别失败:', error);
            this.feedback = {
              isCorrect: false,
              message: '识别失败，请重试'
            };
          }
        }
      }
    });

    app.mount('#app');
  </script>
</body>
</html>