{"ast": null, "code": "'use strict';\n\n/**\n *\n * Entry point for tesseract.js, should be the entry when bundling.\n *\n * @fileoverview entry point for tesseract.js\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nrequire('regenerator-runtime/runtime');\nconst createScheduler = require('./createScheduler');\nconst createWorker = require('./createWorker');\nconst Tesseract = require('./Tesseract');\nconst languages = require('./constants/languages');\nconst OEM = require('./constants/OEM');\nconst PSM = require('./constants/PSM');\nconst {\n  setLogging\n} = require('./utils/log');\nmodule.exports = {\n  languages,\n  OEM,\n  PSM,\n  createScheduler,\n  createWorker,\n  setLogging,\n  ...Tesseract\n};", "map": {"version": 3, "names": ["require", "createScheduler", "createWorker", "Tesseract", "languages", "OEM", "PSM", "setLogging", "module", "exports"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/index.js"], "sourcesContent": ["'use strict';\n\n/**\n *\n * Entry point for tesseract.js, should be the entry when bundling.\n *\n * @fileoverview entry point for tesseract.js\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nrequire('regenerator-runtime/runtime');\nconst createScheduler = require('./createScheduler');\nconst createWorker = require('./createWorker');\nconst Tesseract = require('./Tesseract');\nconst languages = require('./constants/languages');\nconst OEM = require('./constants/OEM');\nconst PSM = require('./constants/PSM');\nconst { setLogging } = require('./utils/log');\n\nmodule.exports = {\n  languages,\n  OEM,\n  PSM,\n  createScheduler,\n  createWorker,\n  setLogging,\n  ...Tesseract,\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,OAAO,CAAC,6BAA6B,CAAC;AACtC,MAAMC,eAAe,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AACpD,MAAME,YAAY,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAC9C,MAAMG,SAAS,GAAGH,OAAO,CAAC,aAAa,CAAC;AACxC,MAAMI,SAAS,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;AAClD,MAAMK,GAAG,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AACtC,MAAMM,GAAG,GAAGN,OAAO,CAAC,iBAAiB,CAAC;AACtC,MAAM;EAAEO;AAAW,CAAC,GAAGP,OAAO,CAAC,aAAa,CAAC;AAE7CQ,MAAM,CAACC,OAAO,GAAG;EACfL,SAAS;EACTC,GAAG;EACHC,GAAG;EACHL,eAAe;EACfC,YAAY;EACZK,UAAU;EACV,GAAGJ;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}