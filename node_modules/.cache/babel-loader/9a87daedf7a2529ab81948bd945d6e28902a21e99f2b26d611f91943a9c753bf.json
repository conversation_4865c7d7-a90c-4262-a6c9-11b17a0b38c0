{"ast": null, "code": "'use strict';\n\n/**\n * send\n *\n * @name send\n * @function send packet to worker and create a job\n * @access public\n */\nmodule.exports = async (worker, packet) => {\n  worker.postMessage(packet);\n};", "map": {"version": 3, "names": ["module", "exports", "worker", "packet", "postMessage"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/worker/browser/send.js"], "sourcesContent": ["'use strict';\n\n/**\n * send\n *\n * @name send\n * @function send packet to worker and create a job\n * @access public\n */\nmodule.exports = async (worker, packet) => {\n  worker.postMessage(packet);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,OAAOC,MAAM,EAAEC,MAAM,KAAK;EACzCD,MAAM,CAACE,WAAW,CAACD,MAAM,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}