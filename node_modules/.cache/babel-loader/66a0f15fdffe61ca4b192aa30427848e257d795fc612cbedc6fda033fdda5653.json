{"ast": null, "code": "'use strict';\n\nconst version = require('../../../package.json').version;\nconst defaultOptions = require('../../constants/defaultOptions');\n\n/*\n * Default options for browser worker\n */\nmodule.exports = {\n  ...defaultOptions,\n  workerPath: `https://cdn.jsdelivr.net/npm/tesseract.js@v${version}/dist/worker.min.js`\n};", "map": {"version": 3, "names": ["version", "require", "defaultOptions", "module", "exports", "worker<PERSON><PERSON>"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/worker/browser/defaultOptions.js"], "sourcesContent": ["'use strict';\n\nconst version = require('../../../package.json').version;\nconst defaultOptions = require('../../constants/defaultOptions');\n\n/*\n * Default options for browser worker\n */\nmodule.exports = {\n  ...defaultOptions,\n  workerPath: `https://cdn.jsdelivr.net/npm/tesseract.js@v${version}/dist/worker.min.js`,\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,OAAO,GAAGC,OAAO,CAAC,uBAAuB,CAAC,CAACD,OAAO;AACxD,MAAME,cAAc,GAAGD,OAAO,CAAC,gCAAgC,CAAC;;AAEhE;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG;EACf,GAAGF,cAAc;EACjBG,UAAU,EAAE,8CAA8CL,OAAO;AACnE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}