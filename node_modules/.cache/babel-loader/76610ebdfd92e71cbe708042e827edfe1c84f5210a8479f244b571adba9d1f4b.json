{"ast": null, "code": "'use strict';\n\nmodule.exports = (worker, handler) => {\n  worker.onmessage = ({\n    data\n  }) => {\n    // eslint-disable-line\n    handler(data);\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "worker", "handler", "onmessage", "data"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/worker/browser/onMessage.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (worker, handler) => {\n  worker.onmessage = ({ data }) => { // eslint-disable-line\n    handler(data);\n  };\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,CAACC,MAAM,EAAEC,OAAO,KAAK;EACpCD,MAAM,CAACE,SAAS,GAAG,CAAC;IAAEC;EAAK,CAAC,KAAK;IAAE;IACjCF,OAAO,CAACE,IAAI,CAAC;EACf,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}