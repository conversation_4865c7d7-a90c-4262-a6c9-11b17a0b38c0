{"ast": null, "code": "'use strict';\n\n/*\n * PSM = Page Segmentation Mode\n */\nmodule.exports = {\n  OSD_ONLY: '0',\n  AUTO_OSD: '1',\n  AUTO_ONLY: '2',\n  AUTO: '3',\n  SINGLE_COLUMN: '4',\n  SINGLE_BLOCK_VERT_TEXT: '5',\n  SINGLE_BLOCK: '6',\n  SINGLE_LINE: '7',\n  SINGLE_WORD: '8',\n  CIRCLE_WORD: '9',\n  SINGLE_CHAR: '10',\n  SPARSE_TEXT: '11',\n  SPARSE_TEXT_OSD: '12',\n  RAW_LINE: '13'\n};", "map": {"version": 3, "names": ["module", "exports", "OSD_ONLY", "AUTO_OSD", "AUTO_ONLY", "AUTO", "SINGLE_COLUMN", "SINGLE_BLOCK_VERT_TEXT", "SINGLE_BLOCK", "SINGLE_LINE", "SINGLE_WORD", "CIRCLE_WORD", "SINGLE_CHAR", "SPARSE_TEXT", "SPARSE_TEXT_OSD", "RAW_LINE"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/constants/PSM.js"], "sourcesContent": ["'use strict';\n\n/*\n * PSM = Page Segmentation Mode\n */\nmodule.exports = {\n  OSD_ONLY: '0',\n  AUTO_OSD: '1',\n  AUTO_ONLY: '2',\n  AUTO: '3',\n  SINGLE_COLUMN: '4',\n  SINGLE_BLOCK_VERT_TEXT: '5',\n  SINGLE_BLOCK: '6',\n  SINGLE_LINE: '7',\n  SINGLE_WORD: '8',\n  CIRCLE_WORD: '9',\n  SINGLE_CHAR: '10',\n  SPARSE_TEXT: '11',\n  SPARSE_TEXT_OSD: '12',\n  RAW_LINE: '13',\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG;EACfC,QAAQ,EAAE,GAAG;EACbC,QAAQ,EAAE,GAAG;EACbC,SAAS,EAAE,GAAG;EACdC,IAAI,EAAE,GAAG;EACTC,aAAa,EAAE,GAAG;EAClBC,sBAAsB,EAAE,GAAG;EAC3BC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE,GAAG;EAChBC,WAAW,EAAE,GAAG;EAChBC,WAAW,EAAE,GAAG;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}