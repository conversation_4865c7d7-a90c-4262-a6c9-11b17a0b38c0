{"ast": null, "code": "'use strict';\n\nconst isBrowser = require('./getEnvironment')('type') === 'browser';\nconst resolveURL = isBrowser ? s => new URL(s, window.location.href).href : s => s; // eslint-disable-line\n\nmodule.exports = options => {\n  const opts = {\n    ...options\n  };\n  ['corePath', 'workerPath', 'langPath'].forEach(key => {\n    if (options[key]) {\n      opts[key] = resolveURL(opts[key]);\n    }\n  });\n  return opts;\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "require", "resolveURL", "s", "URL", "window", "location", "href", "module", "exports", "options", "opts", "for<PERSON>ach", "key"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/utils/resolvePaths.js"], "sourcesContent": ["'use strict';\n\nconst isBrowser = require('./getEnvironment')('type') === 'browser';\n\nconst resolveURL = isBrowser ? s => (new URL(s, window.location.href)).href : s => s; // eslint-disable-line\n\nmodule.exports = (options) => {\n  const opts = { ...options };\n  ['corePath', 'workerPath', 'langPath'].forEach((key) => {\n    if (options[key]) {\n      opts[key] = resolveURL(opts[key]);\n    }\n  });\n  return opts;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,KAAK,SAAS;AAEnE,MAAMC,UAAU,GAAGF,SAAS,GAAGG,CAAC,IAAK,IAAIC,GAAG,CAACD,CAAC,EAAEE,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAAEA,IAAI,GAAGJ,CAAC,IAAIA,CAAC,CAAC,CAAC;;AAEtFK,MAAM,CAACC,OAAO,GAAIC,OAAO,IAAK;EAC5B,MAAMC,IAAI,GAAG;IAAE,GAAGD;EAAQ,CAAC;EAC3B,CAAC,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,CAACE,OAAO,CAAEC,GAAG,IAAK;IACtD,IAAIH,OAAO,CAACG,GAAG,CAAC,EAAE;MAChBF,IAAI,CAACE,GAAG,CAAC,GAAGX,UAAU,CAACS,IAAI,CAACE,GAAG,CAAC,CAAC;IACnC;EACF,CAAC,CAAC;EACF,OAAOF,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}