{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  /*\n   * Use BlobURL for worker script by default\n   * TODO: remove this option\n   *\n   */\n  workerBlobURL: true,\n  logger: () => {}\n};", "map": {"version": 3, "names": ["module", "exports", "workerBlobURL", "logger"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/constants/defaultOptions.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  /*\n   * Use BlobURL for worker script by default\n   * TODO: remove this option\n   *\n   */\n  workerBlobURL: true,\n  logger: () => {},\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACf;AACF;AACA;AACA;AACA;EACEC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAEA,CAAA,KAAM,CAAC;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}