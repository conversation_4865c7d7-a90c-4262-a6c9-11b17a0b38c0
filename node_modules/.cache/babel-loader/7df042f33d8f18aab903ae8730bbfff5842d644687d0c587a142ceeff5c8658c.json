{"ast": null, "code": "'use strict';\n\n/*\n * OEM = OCR Engine Mode, and there are 4 possible modes.\n *\n * By default tesseract.js uses LSTM_ONLY mode.\n *\n */\nmodule.exports = {\n  TESSERACT_ONLY: 0,\n  LSTM_ONLY: 1,\n  TESSERACT_LSTM_COMBINED: 2,\n  DEFAULT: 3\n};", "map": {"version": 3, "names": ["module", "exports", "TESSERACT_ONLY", "LSTM_ONLY", "TESSERACT_LSTM_COMBINED", "DEFAULT"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/constants/OEM.js"], "sourcesContent": ["'use strict';\n\n/*\n * OEM = OCR Engine Mode, and there are 4 possible modes.\n *\n * By default tesseract.js uses LSTM_ONLY mode.\n *\n */\nmodule.exports = {\n  TESSERACT_ONLY: 0,\n  LSTM_ONLY: 1,\n  TESSERACT_LSTM_COMBINED: 2,\n  DEFAULT: 3,\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG;EACfC,cAAc,EAAE,CAAC;EACjBC,SAAS,EAAE,CAAC;EACZC,uBAAuB,EAAE,CAAC;EAC1BC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}