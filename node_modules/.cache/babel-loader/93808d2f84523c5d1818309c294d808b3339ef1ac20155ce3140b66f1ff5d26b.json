{"ast": null, "code": "'use strict';\n\nlet logging = false;\nexports.logging = logging;\nexports.setLogging = _logging => {\n  logging = _logging;\n};\nexports.log = (...args) => logging ? console.log.apply(this, args) : null;", "map": {"version": 3, "names": ["logging", "exports", "setLogging", "_logging", "log", "args", "console", "apply"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/utils/log.js"], "sourcesContent": ["'use strict';\n\nlet logging = false;\n\nexports.logging = logging;\n\nexports.setLogging = (_logging) => {\n  logging = _logging;\n};\n\nexports.log = (...args) => (logging ? console.log.apply(this, args) : null);\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAG,KAAK;AAEnBC,OAAO,CAACD,OAAO,GAAGA,OAAO;AAEzBC,OAAO,CAACC,UAAU,GAAIC,QAAQ,IAAK;EACjCH,OAAO,GAAGG,QAAQ;AACpB,CAAC;AAEDF,OAAO,CAACG,GAAG,GAAG,CAAC,GAAGC,IAAI,KAAML,OAAO,GAAGM,OAAO,CAACF,GAAG,CAACG,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC,GAAG,IAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}