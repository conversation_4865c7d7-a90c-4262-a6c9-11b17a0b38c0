{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, withModifiers as _withModifiers, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  class: \"word-info\"\n};\nconst _hoisted_3 = {\n  class: \"pinyin\"\n};\nconst _hoisted_4 = {\n  class: \"grid-status\"\n};\nconst _hoisted_5 = {\n  style: {\n    \"font-size\": \"12px\",\n    \"margin-top\": \"2px\"\n  }\n};\nconst _hoisted_6 = [\"width\", \"height\"];\nconst _hoisted_7 = {\n  class: \"button-group\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = [\"disabled\"];\nconst _hoisted_10 = [\"disabled\"];\nconst _hoisted_11 = [\"disabled\"];\nconst _hoisted_12 = {\n  class: \"btn-content\"\n};\nconst _hoisted_13 = {\n  key: 0\n};\nconst _hoisted_14 = {\n  key: 1\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"button-group\"\n};\nconst _hoisted_16 = {\n  class: \"recognition-status\"\n};\nconst _hoisted_17 = {\n  key: 1,\n  class: \"recognized-text\"\n};\nconst _hoisted_18 = {\n  key: 2,\n  class: \"debug-panel\"\n};\nconst _hoisted_19 = {\n  class: \"debug-images\"\n};\nconst _hoisted_20 = [\"data-ref\", \"width\", \"height\"];\nconst _hoisted_21 = {\n  class: \"debug-char-label\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" Vue应用根容器 \"), _createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 应用标题 \"), _cache[3] || (_cache[3] = _createElementVNode(\"h1\", {\n    class: \"title\"\n  }, \"中文词语手写测试\", -1 /* HOISTED */)), _createCommentVNode(\" 词语信息显示区域 \"), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 显示当前词语的拼音 \"), _createElementVNode(\"p\", _hoisted_3, \"拼音: \" + _toDisplayString($setup.currentWord.pinyin), 1 /* TEXT */), _createCommentVNode(\" 播放发音按钮 \"), _createElementVNode(\"button\", {\n    onClick: $setup.playPronunciation,\n    class: \"play-btn\"\n  }, \" 播放发音 \")]), _createCommentVNode(\" 字符状态显示区域 - 显示每个字符的识别状态 \"), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentWord.text, (char, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: _normalizeClass([\"char-status\", $setup.getCharStatusClass(index)])\n    }, [_createTextVNode(_toDisplayString(char) + \" \", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.getCharStatusText(index)), 1 /* TEXT */)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 画布容器 - 包含手写区域 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"canvas-container\", {\n      loading: $setup.isRecognizing\n    }])\n  }, [_createCommentVNode(\" 手写画布 - 用户在此区域手写汉字 \"), _createElementVNode(\"canvas\", {\n    ref: \"canvas\",\n    width: $setup.canvasWidth,\n    height: $setup.canvasHeight,\n    class: \"writing-canvas\",\n    onMousedown: $setup.startDrawing,\n    onMousemove: $setup.draw,\n    onMouseup: $setup.stopDrawing,\n    onMouseleave: $setup.stopDrawing,\n    onTouchstart: _withModifiers($setup.startDrawing, [\"prevent\"]),\n    onTouchmove: _withModifiers($setup.draw, [\"prevent\"]),\n    onTouchend: _withModifiers($setup.stopDrawing, [\"prevent\"]),\n    onTouchcancel: _withModifiers($setup.stopDrawing, [\"prevent\"])\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_6)], 2 /* CLASS */), _createCommentVNode(\" 主要操作按钮组 \"), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 清除画布按钮 \"), _createElementVNode(\"button\", {\n    onClick: $setup.clearCanvas,\n    disabled: $setup.isRecognizing,\n    class: \"btn btn-clear\"\n  }, \" 清除画布 \", 8 /* PROPS */, _hoisted_8), _createCommentVNode(\" 撤销上一笔按钮 \"), _createElementVNode(\"button\", {\n    onClick: $setup.undoLastStroke,\n    disabled: $setup.isRecognizing || $setup.strokeHistory.length === 0,\n    class: \"btn btn-undo\"\n  }, \" 撤销 \", 8 /* PROPS */, _hoisted_9), _createCommentVNode(\" 查看提取图像按钮 - 用于调试 \"), _createElementVNode(\"button\", {\n    onClick: $setup.showDebugImages,\n    disabled: $setup.isRecognizing,\n    class: \"btn btn-debug\"\n  }, \" 查看提取图像 \", 8 /* PROPS */, _hoisted_10), _createCommentVNode(\" 提交识别按钮 \"), _createElementVNode(\"button\", {\n    onClick: $setup.recognizeAllChars,\n    disabled: $setup.isRecognizing,\n    class: \"btn btn-submit\"\n  }, [_createElementVNode(\"div\", _hoisted_12, [!$setup.isRecognizing ? (_openBlock(), _createElementBlock(\"span\", _hoisted_13, \"提交识别\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_14, _cache[0] || (_cache[0] = [_createElementVNode(\"div\", {\n    class: \"spinner\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" 识别中... \")])))])], 8 /* PROPS */, _hoisted_11)]), _createCommentVNode(\" 下一个词语按钮 - 当所有字符都识别正确时显示 \"), $setup.allCharsCompleted ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"button\", {\n    onClick: $setup.nextWord,\n    class: \"btn btn-check\",\n    style: {\n      \"width\": \"100%\"\n    }\n  }, \" 下一个词语 \")])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 识别状态显示区域 \"), _createElementVNode(\"div\", _hoisted_16, [$setup.feedback ? (_openBlock(), _createElementBlock(\"p\", {\n    key: 0,\n    class: _normalizeClass([\"feedback\", $setup.feedback.isCorrect ? 'correct' : 'incorrect'])\n  }, _toDisplayString($setup.feedback.message), 3 /* TEXT, CLASS */)) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 识别结果显示 \"), $setup.recognizedText && !$setup.isRecognizing ? (_openBlock(), _createElementBlock(\"p\", _hoisted_17, \" 识别结果: \" + _toDisplayString($setup.recognizedText), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 调试面板 - 显示提取的字符图像 \"), $setup.debugImages.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"debug-title\"\n  }, \"提取的字符图像（用于OCR识别）\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.debugImages, (debugImg, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"debug-char\"\n    }, [_createCommentVNode(\" 显示提取的字符图像 \"), _createElementVNode(\"canvas\", {\n      \"data-ref\": `debugCanvas${index}`,\n      width: debugImg.width,\n      height: debugImg.height,\n      style: {\n        \"width\": \"80px\",\n        \"height\": \"80px\"\n      }\n    }, null, 8 /* PROPS */, _hoisted_20), _createCommentVNode(\" 显示字符信息和识别结果 \"), _createElementVNode(\"div\", _hoisted_21, [_createTextVNode(\" 字符\" + _toDisplayString(index + 1) + \": \" + _toDisplayString($setup.currentWord.text[index]) + \" \", 1 /* TEXT */), _cache[1] || (_cache[1] = _createElementVNode(\"br\", null, null, -1 /* HOISTED */)), $setup.charResults[index] ? (_openBlock(), _createElementBlock(\"span\", {\n      key: 0,\n      style: _normalizeStyle({\n        color: $setup.charResults[index].isCorrect ? '#28a745' : '#dc3545'\n      })\n    }, \" 识别: \" + _toDisplayString($setup.charResults[index].recognized || '无'), 5 /* TEXT, STYLE */)) : _createCommentVNode(\"v-if\", true)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "style", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "currentWord", "pinyin", "onClick", "playPronunciation", "_hoisted_4", "_createElementBlock", "_Fragment", "_renderList", "text", "char", "index", "key", "_normalizeClass", "getCharStatusClass", "_hoisted_5", "getCharStatusText", "loading", "isRecognizing", "ref", "width", "canvasWidth", "height", "canvasHeight", "onMousedown", "startDrawing", "onMousemove", "draw", "onMouseup", "stopDrawing", "onMouseleave", "onTouchstart", "_withModifiers", "onTouchmove", "onTouchend", "onTouchcancel", "_hoisted_7", "clearCanvas", "disabled", "_hoisted_8", "undoLastStroke", "strokeHistory", "length", "_hoisted_9", "showDebugImages", "_hoisted_10", "recognizeAllChars", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_cache", "allCharsCompleted", "_hoisted_15", "nextWord", "_hoisted_16", "feedback", "isCorrect", "message", "recognizedText", "_hoisted_17", "debugImages", "_hoisted_18", "_hoisted_19", "debugImg", "_hoisted_21", "charResults", "_normalizeStyle", "color", "recognized"], "sources": ["/Volumes/Data/Project/VUE/xx/src/App.vue"], "sourcesContent": ["<template>\n  <!-- Vue应用根容器 -->\n  <div class=\"app-container\">\n    <!-- 应用标题 -->\n    <h1 class=\"title\">中文词语手写测试</h1>\n    \n    <!-- 词语信息显示区域 -->\n    <div class=\"word-info\">\n      <!-- 显示当前词语的拼音 -->\n      <p class=\"pinyin\">拼音: {{ currentWord.pinyin }}</p>\n      <!-- 播放发音按钮 -->\n      <button @click=\"playPronunciation\" class=\"play-btn\">\n        播放发音\n      </button>\n    </div>\n    \n    <!-- 字符状态显示区域 - 显示每个字符的识别状态 -->\n    <div class=\"grid-status\">\n      <div \n        v-for=\"(char, index) in currentWord.text\" \n        :key=\"index\"\n        class=\"char-status\"\n        :class=\"getCharStatusClass(index)\"\n      >\n        {{ char }}\n        <div style=\"font-size: 12px; margin-top: 2px;\">\n          {{ getCharStatusText(index) }}\n        </div>\n      </div>\n    </div>\n\n    <!-- 画布容器 - 包含手写区域 -->\n    <div class=\"canvas-container\" :class=\"{ loading: isRecognizing }\">\n      <!-- 手写画布 - 用户在此区域手写汉字 -->\n      <canvas\n        ref=\"canvas\"\n        :width=\"canvasWidth\"\n        :height=\"canvasHeight\"\n        class=\"writing-canvas\"\n        @mousedown=\"startDrawing\"\n        @mousemove=\"draw\"\n        @mouseup=\"stopDrawing\"\n        @mouseleave=\"stopDrawing\"\n        @touchstart.prevent=\"startDrawing\"\n        @touchmove.prevent=\"draw\"\n        @touchend.prevent=\"stopDrawing\"\n        @touchcancel.prevent=\"stopDrawing\"\n      ></canvas>\n    </div>\n    \n    <!-- 主要操作按钮组 -->\n    <div class=\"button-group\">\n      <!-- 清除画布按钮 -->\n      <button\n        @click=\"clearCanvas\"\n        :disabled=\"isRecognizing\"\n        class=\"btn btn-clear\"\n      >\n        清除画布\n      </button>\n      \n      <!-- 撤销上一笔按钮 -->\n      <button\n        @click=\"undoLastStroke\"\n        :disabled=\"isRecognizing || strokeHistory.length === 0\"\n        class=\"btn btn-undo\"\n      >\n        撤销\n      </button>\n      \n      <!-- 查看提取图像按钮 - 用于调试 -->\n      <button\n        @click=\"showDebugImages\"\n        :disabled=\"isRecognizing\"\n        class=\"btn btn-debug\"\n      >\n        查看提取图像\n      </button>\n      \n      <!-- 提交识别按钮 -->\n      <button\n        @click=\"recognizeAllChars\"\n        :disabled=\"isRecognizing\"\n        class=\"btn btn-submit\"\n      >\n        <div class=\"btn-content\">\n          <span v-if=\"!isRecognizing\">提交识别</span>\n          <span v-else>\n            <div class=\"spinner\"></div>\n            识别中...\n          </span>\n        </div>\n      </button>\n    </div>\n    \n    <!-- 下一个词语按钮 - 当所有字符都识别正确时显示 -->\n    <div class=\"button-group\" v-if=\"allCharsCompleted\">\n      <button\n        @click=\"nextWord\"\n        class=\"btn btn-check\"\n        style=\"width: 100%;\"\n      >\n        下一个词语\n      </button>\n    </div>\n\n    <!-- 识别状态显示区域 -->\n    <div class=\"recognition-status\">\n      <p v-if=\"feedback\" class=\"feedback\" :class=\"feedback.isCorrect ? 'correct' : 'incorrect'\">\n        {{ feedback.message }}\n      </p>\n    </div>\n    \n    <!-- 识别结果显示 -->\n    <p v-if=\"recognizedText && !isRecognizing\" class=\"recognized-text\">\n      识别结果: {{ recognizedText }}\n    </p>\n    \n    <!-- 调试面板 - 显示提取的字符图像 -->\n    <div v-if=\"debugImages.length > 0\" class=\"debug-panel\">\n      <div class=\"debug-title\">提取的字符图像（用于OCR识别）</div>\n      <div class=\"debug-images\">\n        <div v-for=\"(debugImg, index) in debugImages\" :key=\"index\" class=\"debug-char\">\n          <!-- 显示提取的字符图像 -->\n          <canvas\n            :data-ref=\"`debugCanvas${index}`\"\n            :width=\"debugImg.width\"\n            :height=\"debugImg.height\"\n            style=\"width: 80px; height: 80px;\"\n          ></canvas>\n          <!-- 显示字符信息和识别结果 -->\n          <div class=\"debug-char-label\">\n            字符{{ index + 1 }}: {{ currentWord.text[index] }}\n            <br>\n            <span v-if=\"charResults[index]\" :style=\"{ color: charResults[index].isCorrect ? '#28a745' : '#dc3545' }\">\n              识别: {{ charResults[index].recognized || '无' }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\n// 引入Vue 3 Composition API\nimport { ref, computed, onMounted, watch, nextTick } from 'vue'\n\n// 引入Tesseract.js OCR库\nimport Tesseract from 'tesseract.js'\n\n// ==================== 响应式数据定义 ====================\n\n// 词语库 - 包含要练习的中文词语及其拼音和发音\nconst words = ref([\n  { text: '你好', pinyin: 'nǐ hǎo', audio: 'https://dict.youdao.com/dictvoice?audio=nihao&type=1' },\n  { text: '谢谢', pinyin: 'xiè xie', audio: 'https://dict.youdao.com/dictvoice?audio=xiexie&type=1' },\n  { text: '再见', pinyin: 'zài jiàn', audio: 'https://dict.youdao.com/dictvoice?audio=zaijian&type=1' },\n  { text: '朋友', pinyin: 'péng yǒu', audio: 'https://dict.youdao.com/dictvoice?audio=pengyou&type=1' },\n  { text: '家人', pinyin: 'jiā rén', audio: 'https://dict.youdao.com/dictvoice?audio=jiaren&type=1' },\n  { text: '学习', pinyin: 'xué xí', audio: 'https://dict.youdao.com/dictvoice?audio=xuexi&type=1' },\n  { text: '工作', pinyin: 'gōng zuò', audio: 'https://dict.youdao.com/dictvoice?audio=gongzuo&type=1' },\n  { text: '时间', pinyin: 'shí jiān', audio: 'https://dict.youdao.com/dictvoice?audio=shijian&type=1' }\n])\n\n// 基础状态数据\nconst currentIndex = ref(0) // 当前词语的索引\nconst recognizedText = ref('') // OCR识别的文本结果\nconst feedback = ref(null) // 反馈信息对象\nconst isDrawing = ref(false) // 是否正在绘制\nconst isRecognizing = ref(false) // 是否正在进行OCR识别\n\n// Canvas相关数据\nconst canvas = ref(null) // Canvas DOM引用\nlet ctx = null // Canvas 2D绘图上下文\nconst gridSize = ref(120) // 每个田字格的大小（像素）\nconst spacing = ref(15) // 田字格之间的间距（像素）\nconst lastX = ref(0) // 上一个绘制点的X坐标\nconst lastY = ref(0) // 上一个绘制点的Y坐标\n\n// 绘制历史和状态\nconst strokeHistory = ref([]) // 存储每一笔的画布状态，用于撤销功能\nconst currentStroke = ref([]) // 当前正在绘制的笔画点集合\nconst charResults = ref([]) // 每个字符的识别结果数组\nconst recognitionProgress = ref({}) // 识别进度状态对象\nconst debugImages = ref([]) // 调试用的提取图像数组\nconst currentDrawingGrid = ref(-1) // 当前正在绘制的格子索引\n\n// ==================== 计算属性 ====================\n\n// 获取当前词语对象\nconst currentWord = computed(() => {\n  return words.value[currentIndex.value]\n})\n\n// 计算画布宽度 - 根据字符数量、格子大小和间距计算\nconst canvasWidth = computed(() => {\n  return currentWord.value.text.length * gridSize.value + (currentWord.value.text.length - 1) * spacing.value\n})\n\n// 计算画布高度 - 等于单个格子的高度\nconst canvasHeight = computed(() => {\n  return gridSize.value\n})\n\n// 检查是否所有字符都识别正确\nconst allCharsCompleted = computed(() => {\n  return charResults.value.length === currentWord.value.text.length &&\n         charResults.value.every(result => result && result.isCorrect)\n})\n\n// ==================== 方法定义 ====================\n\n// 设置高DPI支持 - 确保在高分辨率屏幕上显示清晰\nconst setupHighDPI = () => {\n  const canvasEl = canvas.value\n  const dpr = window.devicePixelRatio || 1 // 获取设备像素比\n\n  // 设置画布的实际像素大小\n  canvasEl.width = canvasWidth.value * dpr\n  canvasEl.height = canvasHeight.value * dpr\n\n  // 设置画布的CSS显示大小\n  canvasEl.style.width = canvasWidth.value + 'px'\n  canvasEl.style.height = canvasHeight.value + 'px'\n\n  // 缩放绘图上下文以匹配设备像素比\n  ctx.scale(dpr, dpr)\n}\n\n// 绘制田字格背景\nconst drawTianziGrid = () => {\n  // 清除整个画布\n  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)\n\n  // 保存当前绘图状态\n  ctx.save()\n\n  // 为每个字符绘制田字格\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const x = i * (gridSize.value + spacing.value) // 计算当前格子的X坐标\n\n    // 绘制外框 - 使用较粗的线条\n    ctx.strokeStyle = '#999999'\n    ctx.lineWidth = 2\n    ctx.strokeRect(x, 0, gridSize.value, gridSize.value)\n\n    // 绘制内部辅助线 - 使用细线\n    ctx.strokeStyle = '#dddddd'\n    ctx.lineWidth = 1\n\n    // 绘制垂直中线\n    ctx.beginPath()\n    ctx.moveTo(x + gridSize.value / 2, 0)\n    ctx.lineTo(x + gridSize.value / 2, gridSize.value)\n    ctx.stroke()\n\n    // 绘制水平中线\n    ctx.beginPath()\n    ctx.moveTo(x, gridSize.value / 2)\n    ctx.lineTo(x + gridSize.value, gridSize.value / 2)\n    ctx.stroke()\n\n    // 绘制对角线 - 使用更淡的颜色\n    ctx.strokeStyle = '#eeeeee'\n    ctx.lineWidth = 0.5\n\n    // 左上到右下对角线\n    ctx.beginPath()\n    ctx.moveTo(x, 0)\n    ctx.lineTo(x + gridSize.value, gridSize.value)\n    ctx.stroke()\n\n    // 右上到左下对角线\n    ctx.beginPath()\n    ctx.moveTo(x + gridSize.value, 0)\n    ctx.lineTo(x, gridSize.value)\n    ctx.stroke()\n  }\n\n  // 恢复绘图状态，为手写做准备\n  ctx.restore()\n  ctx.lineWidth = 3\n  ctx.lineCap = 'round'\n  ctx.lineJoin = 'round'\n  ctx.strokeStyle = '#000000'\n  ctx.fillStyle = '#000000'\n}\n\n// 保存当前画布状态到历史记录（用于撤销功能）\nconst saveCanvasState = () => {\n  const imageData = ctx.getImageData(0, 0, canvas.value.width, canvas.value.height)\n  strokeHistory.value.push(imageData)\n\n  // 限制历史记录数量，避免内存过多占用\n  if (strokeHistory.value.length > 20) {\n    strokeHistory.value.shift() // 删除最早的记录\n  }\n}\n\n// 播放当前词语的发音\nconst playPronunciation = () => {\n  const audio = new Audio(currentWord.value.audio)\n  audio.play().catch(error => {\n    console.error('播放发音失败:', error)\n  })\n}\n\n// 获取事件在画布中的坐标位置\nconst getEventPos = (e) => {\n  const rect = canvas.value.getBoundingClientRect()\n  const clientX = e.clientX || (e.touches && e.touches[0].clientX)\n  const clientY = e.clientY || (e.touches && e.touches[0].clientY)\n  return {\n    x: (clientX - rect.left) * (canvas.value.width / rect.width) / (window.devicePixelRatio || 1),\n    y: (clientY - rect.top) * (canvas.value.height / rect.height) / (window.devicePixelRatio || 1)\n  }\n}\n\n// 获取坐标所在的格子索引\nconst getGridIndex = (x, y) => {\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const gridX = i * (gridSize.value + spacing.value)\n    if (x >= gridX && x <= gridX + gridSize.value && y >= 0 && y <= gridSize.value) {\n      return i // 返回格子索引\n    }\n  }\n  return -1 // 不在任何格子内\n}\n\n// 检查坐标是否在任何格子内\nconst isInAnyGrid = (x, y) => {\n  return getGridIndex(x, y) !== -1\n}\n\n// 开始绘制 - 鼠标按下或触摸开始时调用\nconst startDrawing = (e) => {\n  if (isRecognizing.value) return // 识别中时禁止绘制\n\n  const pos = getEventPos(e)\n\n  // 检查是否在任何格子内\n  const gridIndex = getGridIndex(pos.x, pos.y)\n  if (gridIndex === -1) return // 不在格子内则不开始绘制\n\n  // 记录当前绘制的格子索引\n  currentDrawingGrid.value = gridIndex\n\n  // 在开始新的笔画前保存当前状态\n  saveCanvasState()\n\n  isDrawing.value = true\n  lastX.value = pos.x\n  lastY.value = pos.y\n  currentStroke.value = [pos]\n  ctx.beginPath()\n  ctx.moveTo(pos.x, pos.y)\n}\n\n// 绘制过程 - 鼠标移动或触摸移动时调用\nconst draw = (e) => {\n  if (!isDrawing.value || isRecognizing.value) return\n  const pos = getEventPos(e)\n\n  // 检查是否还在同一个格子内\n  const gridIndex = getGridIndex(pos.x, pos.y)\n  if (gridIndex !== currentDrawingGrid.value) {\n    stopDrawing() // 离开当前格子则停止绘制\n    return\n  }\n\n  // 记录当前笔画的点\n  currentStroke.value.push(pos)\n\n  // 使用二次贝塞尔曲线使线条更平滑\n  ctx.quadraticCurveTo(lastX.value, lastY.value, (pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2)\n  ctx.stroke()\n  ctx.beginPath()\n  ctx.moveTo((pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2)\n\n  lastX.value = pos.x\n  lastY.value = pos.y\n}\n\n// 停止绘制 - 鼠标抬起或触摸结束时调用\nconst stopDrawing = () => {\n  if (isDrawing.value) {\n    isDrawing.value = false\n    ctx.closePath()\n    currentStroke.value = [] // 清空当前笔画\n    currentDrawingGrid.value = -1 // 重置当前绘制格子\n  }\n}\n\n// 重置词语状态 - 切换到新词语时调用\nconst resetWordState = () => {\n  charResults.value = []\n  recognitionProgress.value = {}\n  debugImages.value = []\n  clearCanvas()\n}\n\n// 清除画布\nconst clearCanvas = () => {\n  drawTianziGrid() // 清除后重新绘制田字格\n  strokeHistory.value = [] // 清空撤销历史\n  currentStroke.value = []\n  recognizedText.value = ''\n  feedback.value = null\n}\n\n// 撤销上一笔绘制\nconst undoLastStroke = () => {\n  if (strokeHistory.value.length === 0) return\n\n  // 恢复到上一个状态\n  const lastState = strokeHistory.value.pop()\n  ctx.putImageData(lastState, 0, 0)\n\n  recognizedText.value = ''\n  feedback.value = null\n}\n\n// 获取字符状态的CSS类名\nconst getCharStatusClass = (index) => {\n  if (recognitionProgress.value[index] === 'recognizing') return 'current'\n  if (charResults.value[index]) {\n    return charResults.value[index].isCorrect ? 'correct' : 'incorrect'\n  }\n  return 'pending'\n}\n\n// 获取字符状态的显示文本\nconst getCharStatusText = (index) => {\n  if (recognitionProgress.value[index] === 'recognizing') return '识别中'\n  if (charResults.value[index]) {\n    return charResults.value[index].isCorrect ? '正确' : '错误'\n  }\n  return '待识别'\n}\n\n// 显示调试图像 - 提取每个字符的图像用于调试\nconst showDebugImages = () => {\n  debugImages.value = []\n\n  // 先在原canvas上绘制提取区域边界（用于调试）\n  drawExtractionBounds()\n\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const charCanvas = extractCharCanvasAtIndex(i)\n\n    // 保存图像数据\n    debugImages.value.push({\n      canvas: charCanvas,\n      width: charCanvas.width,\n      height: charCanvas.height,\n      imageData: charCanvas.getContext('2d').getImageData(0, 0, charCanvas.width, charCanvas.height)\n    })\n  }\n\n  // 等待Vue更新DOM后绘制调试图像\n  nextTick(() => {\n    renderDebugImages()\n  })\n}\n\n// 绘制提取区域边界 - 用于调试\nconst drawExtractionBounds = () => {\n  ctx.save()\n  ctx.strokeStyle = 'red'\n  ctx.lineWidth = 2\n  ctx.setLineDash([5, 5]) // 虚线样式\n\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const x = i * (gridSize.value + spacing.value)\n    ctx.strokeRect(x, 0, gridSize.value, gridSize.value)\n  }\n\n  ctx.restore()\n\n  // 2秒后清除边界线\n  setTimeout(() => {\n    drawTianziGrid()\n  }, 2000)\n}\n\n// 渲染调试图像到调试面板\nconst renderDebugImages = () => {\n  debugImages.value.forEach((debugImg, index) => {\n    const canvasRef = document.querySelector(`[data-ref=\"debugCanvas${index}\"]`)\n    if (canvasRef) {\n      const ctx = canvasRef.getContext('2d')\n\n      // 清除画布\n      ctx.clearRect(0, 0, canvasRef.width, canvasRef.height)\n\n      // 绘制提取的图像\n      ctx.putImageData(debugImg.imageData, 0, 0)\n    }\n  })\n}\n\n// 提取指定索引位置的字符画布\nconst extractCharCanvasAtIndex = (index) => {\n  // 创建临时canvas，只包含指定字符格子的内容\n  const tempCanvas = document.createElement('canvas')\n  const tempCtx = tempCanvas.getContext('2d')\n\n  // 设置canvas大小为单个格子大小\n  tempCanvas.width = gridSize.value\n  tempCanvas.height = gridSize.value\n\n  // 填充白色背景\n  tempCtx.fillStyle = 'white'\n  tempCtx.fillRect(0, 0, gridSize.value, gridSize.value)\n\n  // 计算指定格子在原canvas中的位置\n  const sourceX = index * (gridSize.value + spacing.value)\n\n  console.log(`提取字符${index + 1}:`, {\n    sourceX: sourceX,\n    sourceY: 0,\n    width: gridSize.value,\n    height: gridSize.value,\n    canvasWidth: canvas.value.width,\n    canvasHeight: canvas.value.height,\n    displayWidth: canvas.value.style.width,\n    displayHeight: canvas.value.style.height\n  })\n\n  // 需要考虑高DPI缩放\n  const dpr = window.devicePixelRatio || 1\n  const actualSourceX = sourceX * dpr\n  const actualSourceY = 0\n  const actualWidth = gridSize.value * dpr\n  const actualHeight = gridSize.value * dpr\n\n  // 从原canvas中提取指定格子的内容\n  tempCtx.drawImage(\n    canvas.value,\n    actualSourceX, actualSourceY, actualWidth, actualHeight,  // 源区域（考虑DPI）\n    0, 0, gridSize.value, gridSize.value                        // 目标区域\n  )\n\n  return tempCanvas\n}\n\n// 识别所有字符 - 主要的OCR识别函数\nconst recognizeAllChars = async () => {\n  if (isRecognizing.value) return\n\n  isRecognizing.value = true\n  recognizedText.value = ''\n  feedback.value = null\n  charResults.value = []\n  recognitionProgress.value = {}\n\n  // 先显示调试图像\n  showDebugImages()\n\n  try {\n    // 顺序识别每个字符（一个一个提交OCR）\n    for (let i = 0; i < currentWord.value.text.length; i++) {\n      recognitionProgress.value[i] = 'recognizing'\n\n      try {\n        console.log(`开始识别第${i + 1}个字符: ${currentWord.value.text[i]}`)\n        const result = await recognizeCharAtIndex(i)\n        charResults.value[i] = result\n        recognitionProgress.value[i] = 'completed'\n        console.log(`第${i + 1}个字符识别完成:`, result)\n\n        // 短暂延迟，让用户看到进度\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n      } catch (error) {\n        console.error(`第${i + 1}个字符识别失败:`, error)\n        charResults.value[i] = {\n          recognized: '',\n          expected: currentWord.value.text[i],\n          isCorrect: false\n        }\n        recognitionProgress.value[i] = 'completed'\n      }\n    }\n\n    // 处理最终结果\n    const correctCount = charResults.value.filter(r => r.isCorrect).length\n    const totalCount = charResults.value.length\n    const allCorrect = correctCount === totalCount\n\n    let resultMessage = ''\n    if (allCorrect) {\n      resultMessage = `全部正确！识别结果：${charResults.value.map(r => r.recognized).join('')}`\n    } else {\n      resultMessage = `识别完成：${correctCount}/${totalCount} 正确`\n      // 显示详细结果\n      const details = charResults.value.map((r, i) =>\n        `${currentWord.value.text[i]}→${r.recognized}${r.isCorrect ? '✓' : '✗'}`\n      ).join(' ')\n      resultMessage += `\\n详情：${details}`\n    }\n\n    feedback.value = {\n      isCorrect: allCorrect,\n      message: resultMessage\n    }\n\n    if (allCorrect) {\n      setTimeout(() => {\n        feedback.value = {\n          isCorrect: true,\n          message: '恭喜！可以进入下一个词语了'\n        }\n      }, 2000)\n    }\n\n  } catch (error) {\n    console.error('识别失败:', error)\n    feedback.value = {\n      isCorrect: false,\n      message: '识别失败，请重试'\n    }\n  } finally {\n    isRecognizing.value = false\n  }\n}\n\n// 识别指定索引位置的字符\nconst recognizeCharAtIndex = async (index) => {\n  try {\n    // 创建只包含指定格子的canvas\n    const charCanvas = extractCharCanvasAtIndex(index)\n\n    const result = await Tesseract.recognize(\n      charCanvas,\n      'chi_sim', // 简体中文\n      {\n        logger: (m) => {\n          if (m.status === 'recognizing text') {\n            console.log(`字符${index + 1}识别进度: ${Math.round(m.progress * 100)}%`)\n          }\n        },\n        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_CHAR, // 单字符模式\n        tessedit_char_whitelist: currentWord.value.text, // 字符白名单\n        preserve_interword_spaces: '0'\n      }\n    )\n\n    let recognizedText = result.data.text.trim()\n\n    // 清理识别结果，只保留中文\n    recognizedText = recognizedText.replace(/[^\\u4e00-\\u9fa5]/g, '')\n\n    // 检查字符\n    const expectedChar = currentWord.value.text[index]\n    const isCorrect = recognizedText === expectedChar\n\n    return {\n      recognized: recognizedText,\n      expected: expectedChar,\n      isCorrect: isCorrect\n    }\n\n  } catch (error) {\n    console.error(`字符${index + 1}识别失败:`, error)\n    throw error\n  }\n}\n\n// 切换到下一个词语\nconst nextWord = () => {\n  currentIndex.value = (currentIndex.value + 1) % words.value.length\n}\n\n// ==================== 生命周期钩子 ====================\n\n// 组件挂载后的钩子函数\nonMounted(() => {\n  // 获取画布的2D绘图上下文\n  ctx = canvas.value.getContext('2d')\n\n  // 设置画笔属性\n  ctx.lineWidth = 3 // 线条宽度\n  ctx.lineCap = 'round' // 线条端点样式为圆形\n  ctx.lineJoin = 'round' // 线条连接点样式为圆形\n  ctx.strokeStyle = '#000000' // 线条颜色为黑色\n  ctx.fillStyle = '#000000' // 填充颜色为黑色\n\n  // 设置高DPI支持，确保在高分辨率屏幕上显示清晰\n  setupHighDPI()\n\n  // 绘制田字格背景\n  drawTianziGrid()\n})\n\n// ==================== 监听器 ====================\n\n// 当前词语改变时重置状态\nwatch(currentWord, () => {\n  resetWordState()\n})\n</script>\n\n<style>\n/* 全局样式重置 - 清除默认边距和内边距 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box; /* 使用边框盒模型 */\n}\n\n/* 页面主体样式 */\nbody {\n  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif; /* 中文友好字体 */\n  background-color: #f5f5f5; /* 浅灰色背景 */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh; /* 最小高度为视窗高度 */\n  padding: 20px;\n}\n\n/* 应用主容器样式 */\n.app-container {\n  background: white;\n  padding: 30px;\n  border-radius: 12px; /* 圆角边框 */\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* 阴影效果 */\n  width: 100%;\n  max-width: 600px; /* 最大宽度限制 */\n}\n\n/* 标题样式 */\n.title {\n  font-size: 28px;\n  font-weight: bold;\n  text-align: center;\n  margin-bottom: 30px;\n  color: #333;\n}\n\n/* 词语信息区域样式 */\n.word-info {\n  margin-bottom: 25px;\n  text-align: center;\n}\n\n/* 拼音显示样式 */\n.pinyin {\n  font-size: 20px;\n  color: #666;\n  margin-bottom: 15px;\n}\n\n/* 播放发音按钮样式 */\n.play-btn {\n  background: #007bff; /* 蓝色背景 */\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 16px;\n  transition: background-color 0.3s; /* 背景色过渡动画 */\n}\n\n/* 播放按钮悬停效果 */\n.play-btn:hover {\n  background: #0056b3; /* 深蓝色 */\n}\n\n/* 画布容器样式 */\n.canvas-container {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  margin: 25px 0;\n  background: #fafafa; /* 浅灰色背景 */\n  border-radius: 8px;\n  padding: 20px;\n}\n\n/* 手写画布样式 */\n.writing-canvas {\n  background: white;\n  cursor: crosshair; /* 十字光标 */\n  border-radius: 4px;\n}\n\n/* 加载状态样式 - 识别时的半透明效果 */\n.loading {\n  opacity: 0.6;\n  pointer-events: none; /* 禁用鼠标事件 */\n}\n\n/* 按钮组容器样式 */\n.button-group {\n  display: flex;\n  justify-content: space-between;\n  gap: 15px; /* 按钮间距 */\n  margin-bottom: 25px;\n}\n\n/* 通用按钮样式 */\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 16px;\n  transition: all 0.3s; /* 所有属性过渡动画 */\n  flex: 1; /* 等宽分布 */\n}\n\n/* 禁用状态按钮样式 */\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 清除按钮样式 */\n.btn-clear {\n  background: #6c757d; /* 灰色 */\n  color: white;\n}\n\n.btn-clear:hover:not(:disabled) {\n  background: #545b62; /* 深灰色 */\n}\n\n/* 提交按钮样式 */\n.btn-submit {\n  background: #28a745; /* 绿色 */\n  color: white;\n}\n\n.btn-submit:hover:not(:disabled) {\n  background: #1e7e34; /* 深绿色 */\n}\n\n/* 撤销按钮样式 */\n.btn-undo {\n  background: #ffc107; /* 黄色 */\n  color: #212529; /* 深色文字 */\n}\n\n.btn-undo:hover:not(:disabled) {\n  background: #e0a800; /* 深黄色 */\n}\n\n/* 检查按钮样式 */\n.btn-check {\n  background: #17a2b8; /* 青色 */\n  color: white;\n}\n\n.btn-check:hover:not(:disabled) {\n  background: #138496; /* 深青色 */\n}\n\n/* 字符状态显示区域样式 */\n.grid-status {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin: 15px 0;\n  flex-wrap: wrap; /* 允许换行 */\n}\n\n/* 单个字符状态样式 */\n.char-status {\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 14px;\n  font-weight: bold;\n  min-width: 60px;\n  text-align: center;\n}\n\n/* 待识别状态样式 */\n.char-status.pending {\n  background: #f8f9fa;\n  color: #6c757d;\n  border: 2px solid #dee2e6;\n}\n\n/* 识别正确状态样式 */\n.char-status.correct {\n  background: #d4edda;\n  color: #155724;\n  border: 2px solid #c3e6cb;\n}\n\n/* 识别错误状态样式 */\n.char-status.incorrect {\n  background: #f8d7da;\n  color: #721c24;\n  border: 2px solid #f5c6cb;\n}\n\n/* 当前识别中状态样式 */\n.char-status.current {\n  background: #fff3cd;\n  color: #856404;\n  border: 2px solid #ffeaa7;\n}\n\n/* 调试面板样式 */\n.debug-panel {\n  margin: 20px 0;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #dee2e6;\n}\n\n/* 调试标题样式 */\n.debug-title {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #495057;\n}\n\n/* 调试图像容器样式 */\n.debug-images {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n/* 单个调试字符样式 */\n.debug-char {\n  text-align: center;\n}\n\n/* 调试画布样式 */\n.debug-char canvas {\n  border: 1px solid #ccc;\n  background: white;\n}\n\n/* 调试字符标签样式 */\n.debug-char-label {\n  font-size: 12px;\n  margin-top: 5px;\n  color: #666;\n}\n\n/* 调试按钮样式 */\n.btn-debug {\n  background: #6f42c1; /* 紫色 */\n  color: white;\n}\n\n.btn-debug:hover:not(:disabled) {\n  background: #5a32a3; /* 深紫色 */\n}\n\n/* 按钮内容容器样式 */\n.btn-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 加载动画样式 */\n.spinner {\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #ffffff;\n  border-radius: 50%;\n  width: 18px;\n  height: 18px;\n  animation: spin 1s linear infinite; /* 旋转动画 */\n  margin-right: 8px;\n}\n\n/* 旋转动画关键帧 */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 识别状态显示区域样式 */\n.recognition-status {\n  min-height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15px;\n}\n\n/* 反馈信息样式 */\n.feedback {\n  text-align: center;\n  font-size: 18px;\n  font-weight: bold;\n}\n\n/* 正确反馈样式 */\n.feedback.correct {\n  color: #28a745;\n}\n\n/* 错误反馈样式 */\n.feedback.incorrect {\n  color: #dc3545;\n}\n\n/* 识别结果文本样式 */\n.recognized-text {\n  text-align: center;\n  color: #666;\n  font-size: 16px;\n  margin-top: 10px;\n}\n</style>\n"], "mappings": ";;EAEOA,KAAK,EAAC;AAAe;;EAKnBA,KAAK,EAAC;AAAW;;EAEjBA,KAAK,EAAC;AAAQ;;EAQdA,KAAK,EAAC;AAAa;;EAQfC,KAAyC,EAAzC;IAAA;IAAA;EAAA;AAAyC;;;EA0B7CD,KAAK,EAAC;AAAc;;;;;;EAkChBA,KAAK,EAAC;AAAa;;;;;;;;;EAWvBA,KAAK,EAAC;;;EAWNA,KAAK,EAAC;AAAoB;;;EAOYA,KAAK,EAAC;;;;EAKdA,KAAK,EAAC;;;EAElCA,KAAK,EAAC;AAAc;;;EAUhBA,KAAK,EAAC;AAAkB;;6DAlIrCE,mBAAA,cAAiB,EACjBC,mBAAA,CA2IM,OA3INC,UA2IM,GA1IJF,mBAAA,UAAa,E,0BACbC,mBAAA,CAA+B;IAA3BH,KAAK,EAAC;EAAO,GAAC,UAAQ,sBAE1BE,mBAAA,cAAiB,EACjBC,mBAAA,CAOM,OAPNE,UAOM,GANJH,mBAAA,eAAkB,EAClBC,mBAAA,CAAkD,KAAlDG,UAAkD,EAAhC,MAAI,GAAAC,gBAAA,CAAGC,MAAA,CAAAC,WAAW,CAACC,MAAM,kBAC3CR,mBAAA,YAAe,EACfC,mBAAA,CAES;IAFAQ,OAAK,EAAEH,MAAA,CAAAI,iBAAiB;IAAEZ,KAAK,EAAC;KAAW,QAEpD,E,GAGFE,mBAAA,4BAA+B,EAC/BC,mBAAA,CAYM,OAZNU,UAYM,I,kBAXJC,mBAAA,CAUMC,SAAA,QAAAC,WAAA,CAToBR,MAAA,CAAAC,WAAW,CAACQ,IAAI,GAAhCC,IAAI,EAAEC,KAAK;yBADrBL,mBAAA,CAUM;MARHM,GAAG,EAAED,KAAK;MACXnB,KAAK,EAAAqB,eAAA,EAAC,aAAa,EACXb,MAAA,CAAAc,kBAAkB,CAACH,KAAK;0CAE7BD,IAAI,IAAG,GACV,iBAAAf,mBAAA,CAEM,OAFNoB,UAEM,EAAAhB,gBAAA,CADDC,MAAA,CAAAgB,iBAAiB,CAACL,KAAK,kB;oCAKhCjB,mBAAA,mBAAsB,EACtBC,mBAAA,CAgBM;IAhBDH,KAAK,EAAAqB,eAAA,EAAC,kBAAkB;MAAAI,OAAA,EAAoBjB,MAAA,CAAAkB;IAAa;MAC5DxB,mBAAA,uBAA0B,EAC1BC,mBAAA,CAaU;IAZRwB,GAAG,EAAC,QAAQ;IACXC,KAAK,EAAEpB,MAAA,CAAAqB,WAAW;IAClBC,MAAM,EAAEtB,MAAA,CAAAuB,YAAY;IACrB/B,KAAK,EAAC,gBAAgB;IACrBgC,WAAS,EAAExB,MAAA,CAAAyB,YAAY;IACvBC,WAAS,EAAE1B,MAAA,CAAA2B,IAAI;IACfC,SAAO,EAAE5B,MAAA,CAAA6B,WAAW;IACpBC,YAAU,EAAE9B,MAAA,CAAA6B,WAAW;IACvBE,YAAU,EAAAC,cAAA,CAAUhC,MAAA,CAAAyB,YAAY;IAChCQ,WAAS,EAAAD,cAAA,CAAUhC,MAAA,CAAA2B,IAAI;IACvBO,UAAQ,EAAAF,cAAA,CAAUhC,MAAA,CAAA6B,WAAW;IAC7BM,aAAW,EAAAH,cAAA,CAAUhC,MAAA,CAAA6B,WAAW;yEAIrCnC,mBAAA,aAAgB,EAChBC,mBAAA,CA0CM,OA1CNyC,UA0CM,GAzCJ1C,mBAAA,YAAe,EACfC,mBAAA,CAMS;IALNQ,OAAK,EAAEH,MAAA,CAAAqC,WAAW;IAClBC,QAAQ,EAAEtC,MAAA,CAAAkB,aAAa;IACxB1B,KAAK,EAAC;KACP,QAED,iBAAA+C,UAAA,GAEA7C,mBAAA,aAAgB,EAChBC,mBAAA,CAMS;IALNQ,OAAK,EAAEH,MAAA,CAAAwC,cAAc;IACrBF,QAAQ,EAAEtC,MAAA,CAAAkB,aAAa,IAAIlB,MAAA,CAAAyC,aAAa,CAACC,MAAM;IAChDlD,KAAK,EAAC;KACP,MAED,iBAAAmD,UAAA,GAEAjD,mBAAA,qBAAwB,EACxBC,mBAAA,CAMS;IALNQ,OAAK,EAAEH,MAAA,CAAA4C,eAAe;IACtBN,QAAQ,EAAEtC,MAAA,CAAAkB,aAAa;IACxB1B,KAAK,EAAC;KACP,UAED,iBAAAqD,WAAA,GAEAnD,mBAAA,YAAe,EACfC,mBAAA,CAYS;IAXNQ,OAAK,EAAEH,MAAA,CAAA8C,iBAAiB;IACxBR,QAAQ,EAAEtC,MAAA,CAAAkB,aAAa;IACxB1B,KAAK,EAAC;MAENG,mBAAA,CAMM,OANNoD,WAMM,G,CALS/C,MAAA,CAAAkB,aAAa,I,cAA1BZ,mBAAA,CAAuC,QAAA0C,WAAA,EAAX,MAAI,M,cAChC1C,mBAAA,CAGO,QAAA2C,WAAA,EAAAC,MAAA,QAAAA,MAAA,OAFLvD,mBAAA,CAA2B;IAAtBH,KAAK,EAAC;EAAS,4B,iBAAO,UAE7B,E,uCAKNE,mBAAA,6BAAgC,EACAM,MAAA,CAAAmD,iBAAiB,I,cAAjD7C,mBAAA,CAQM,OARN8C,WAQM,GAPJzD,mBAAA,CAMS;IALNQ,OAAK,EAAEH,MAAA,CAAAqD,QAAQ;IAChB7D,KAAK,EAAC,eAAe;IACrBC,KAAoB,EAApB;MAAA;IAAA;KACD,SAED,E,wCAGFC,mBAAA,cAAiB,EACjBC,mBAAA,CAIM,OAJN2D,WAIM,GAHKtD,MAAA,CAAAuD,QAAQ,I,cAAjBjD,mBAAA,CAEI;;IAFed,KAAK,EAAAqB,eAAA,EAAC,UAAU,EAASb,MAAA,CAAAuD,QAAQ,CAACC,SAAS;sBACzDxD,MAAA,CAAAuD,QAAQ,CAACE,OAAO,2B,qCAIvB/D,mBAAA,YAAe,EACNM,MAAA,CAAA0D,cAAc,KAAK1D,MAAA,CAAAkB,aAAa,I,cAAzCZ,mBAAA,CAEI,KAFJqD,WAEI,EAF+D,SAC3D,GAAA5D,gBAAA,CAAGC,MAAA,CAAA0D,cAAc,oB,mCAGzBhE,mBAAA,sBAAyB,EACdM,MAAA,CAAA4D,WAAW,CAAClB,MAAM,Q,cAA7BpC,mBAAA,CAqBM,OArBNuD,WAqBM,G,0BApBJlE,mBAAA,CAA+C;IAA1CH,KAAK,EAAC;EAAa,GAAC,kBAAgB,sBACzCG,mBAAA,CAkBM,OAlBNmE,WAkBM,I,kBAjBJxD,mBAAA,CAgBMC,SAAA,QAAAC,WAAA,CAhB2BR,MAAA,CAAA4D,WAAW,GAA/BG,QAAQ,EAAEpD,KAAK;yBAA5BL,mBAAA,CAgBM;MAhByCM,GAAG,EAAED,KAAK;MAAEnB,KAAK,EAAC;QAC/DE,mBAAA,eAAkB,EAClBC,mBAAA,CAKU;MAJP,UAAQ,gBAAgBgB,KAAK;MAC7BS,KAAK,EAAE2C,QAAQ,CAAC3C,KAAK;MACrBE,MAAM,EAAEyC,QAAQ,CAACzC,MAAM;MACxB7B,KAAkC,EAAlC;QAAA;QAAA;MAAA;0CAEFC,mBAAA,iBAAoB,EACpBC,mBAAA,CAMM,OANNqE,WAMM,G,iBANwB,KAC1B,GAAAjE,gBAAA,CAAGY,KAAK,QAAO,IAAE,GAAAZ,gBAAA,CAAGC,MAAA,CAAAC,WAAW,CAACQ,IAAI,CAACE,KAAK,KAAI,GAChD,iB,0BAAAhB,mBAAA,CAAI,sCACQK,MAAA,CAAAiE,WAAW,CAACtD,KAAK,K,cAA7BL,mBAAA,CAEO;;MAF0Bb,KAAK,EAAAyE,eAAA;QAAAC,KAAA,EAAWnE,MAAA,CAAAiE,WAAW,CAACtD,KAAK,EAAE6C,SAAS;MAAA;OAA4B,OACnG,GAAAzD,gBAAA,CAAGC,MAAA,CAAAiE,WAAW,CAACtD,KAAK,EAAEyD,UAAU,kC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}