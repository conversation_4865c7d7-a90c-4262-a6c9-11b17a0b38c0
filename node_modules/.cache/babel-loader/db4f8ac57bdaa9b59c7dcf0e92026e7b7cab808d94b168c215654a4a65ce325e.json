{"ast": null, "code": "'use strict';\n\nconst getId = require('./utils/getId');\nlet jobCounter = 0;\nmodule.exports = ({\n  id: _id,\n  action,\n  payload = {}\n}) => {\n  let id = _id;\n  if (typeof id === 'undefined') {\n    id = getId('Job', jobCounter);\n    jobCounter += 1;\n  }\n  return {\n    id,\n    action,\n    payload\n  };\n};", "map": {"version": 3, "names": ["getId", "require", "jobCounter", "module", "exports", "id", "_id", "action", "payload"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/createJob.js"], "sourcesContent": ["'use strict';\n\nconst getId = require('./utils/getId');\n\nlet jobCounter = 0;\n\nmodule.exports = ({\n  id: _id,\n  action,\n  payload = {},\n}) => {\n  let id = _id;\n  if (typeof id === 'undefined') {\n    id = getId('Job', jobCounter);\n    jobCounter += 1;\n  }\n\n  return {\n    id,\n    action,\n    payload,\n  };\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,KAAK,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEtC,IAAIC,UAAU,GAAG,CAAC;AAElBC,MAAM,CAACC,OAAO,GAAG,CAAC;EAChBC,EAAE,EAAEC,GAAG;EACPC,MAAM;EACNC,OAAO,GAAG,CAAC;AACb,CAAC,KAAK;EACJ,IAAIH,EAAE,GAAGC,GAAG;EACZ,IAAI,OAAOD,EAAE,KAAK,WAAW,EAAE;IAC7BA,EAAE,GAAGL,KAAK,CAAC,KAAK,EAAEE,UAAU,CAAC;IAC7BA,UAAU,IAAI,CAAC;EACjB;EAEA,OAAO;IACLG,EAAE;IACFE,MAAM;IACNC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}