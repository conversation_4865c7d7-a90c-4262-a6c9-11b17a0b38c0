{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.filter.js\");\nconst resolvePaths = require('./utils/resolvePaths');\nconst createJob = require('./createJob');\nconst {\n  log\n} = require('./utils/log');\nconst getId = require('./utils/getId');\nconst OEM = require('./constants/OEM');\nconst {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  loadImage,\n  send\n} = require('./worker/node');\nlet workerCounter = 0;\nmodule.exports = async (langs = 'eng', oem = OEM.LSTM_ONLY, _options = {}, config = {}) => {\n  const id = getId('Worker', workerCounter);\n  const {\n    logger,\n    errorHandler,\n    ...options\n  } = resolvePaths({\n    ...defaultOptions,\n    ..._options\n  });\n  const promises = {};\n\n  // Current langs, oem, and config file.\n  // Used if the user ever re-initializes the worker using `worker.reinitialize`.\n  const currentLangs = typeof langs === 'string' ? langs.split('+') : langs;\n  let currentOem = oem;\n  let currentConfig = config;\n  const lstmOnlyCore = [OEM.DEFAULT, OEM.LSTM_ONLY].includes(oem) && !options.legacyCore;\n  let workerResReject;\n  let workerResResolve;\n  const workerRes = new Promise((resolve, reject) => {\n    workerResResolve = resolve;\n    workerResReject = reject;\n  });\n  const workerError = event => {\n    workerResReject(event.message);\n  };\n  let worker = spawnWorker(options);\n  worker.onerror = workerError;\n  workerCounter += 1;\n  const startJob = ({\n    id: jobId,\n    action,\n    payload\n  }) => new Promise((resolve, reject) => {\n    log(`[${id}]: Start ${jobId}, action=${action}`);\n    // Using both `action` and `jobId` in case user provides non-unique `jobId`.\n    const promiseId = `${action}-${jobId}`;\n    promises[promiseId] = {\n      resolve,\n      reject\n    };\n    send(worker, {\n      workerId: id,\n      jobId,\n      action,\n      payload\n    });\n  });\n  const load = () => console.warn('`load` is depreciated and should be removed from code (workers now come pre-loaded)');\n  const loadInternal = jobId => startJob(createJob({\n    id: jobId,\n    action: 'load',\n    payload: {\n      options: {\n        lstmOnly: lstmOnlyCore,\n        corePath: options.corePath,\n        logging: options.logging\n      }\n    }\n  }));\n  const writeText = (path, text, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'FS',\n    payload: {\n      method: 'writeFile',\n      args: [path, text]\n    }\n  }));\n  const readText = (path, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'FS',\n    payload: {\n      method: 'readFile',\n      args: [path, {\n        encoding: 'utf8'\n      }]\n    }\n  }));\n  const removeFile = (path, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'FS',\n    payload: {\n      method: 'unlink',\n      args: [path]\n    }\n  }));\n  const FS = (method, args, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'FS',\n    payload: {\n      method,\n      args\n    }\n  }));\n  const loadLanguageInternal = (_langs, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'loadLanguage',\n    payload: {\n      langs: _langs,\n      options: {\n        langPath: options.langPath,\n        dataPath: options.dataPath,\n        cachePath: options.cachePath,\n        cacheMethod: options.cacheMethod,\n        gzip: options.gzip,\n        lstmOnly: [OEM.DEFAULT, OEM.LSTM_ONLY].includes(currentOem) && !options.legacyLang\n      }\n    }\n  }));\n  const initializeInternal = (_langs, _oem, _config, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'initialize',\n    payload: {\n      langs: _langs,\n      oem: _oem,\n      config: _config\n    }\n  }));\n  const reinitialize = (langs = 'eng', oem, config, jobId) => {\n    // eslint-disable-line\n\n    if (lstmOnlyCore && [OEM.TESSERACT_ONLY, OEM.TESSERACT_LSTM_COMBINED].includes(oem)) throw Error('Legacy model requested but code missing.');\n    const _oem = oem || currentOem;\n    currentOem = _oem;\n    const _config = config || currentConfig;\n    currentConfig = _config;\n\n    // Only load langs that are not already loaded.\n    // This logic fails if the user downloaded the LSTM-only English data for a language\n    // and then uses `worker.reinitialize` to switch to the Legacy engine.\n    // However, the correct data will still be downloaded after initialization fails\n    // and this can be avoided entirely if the user loads the correct data ahead of time.\n    const langsArr = typeof langs === 'string' ? langs.split('+') : langs;\n    const _langs = langsArr.filter(x => !currentLangs.includes(x));\n    currentLangs.push(..._langs);\n    if (_langs.length > 0) {\n      return loadLanguageInternal(_langs, jobId).then(() => initializeInternal(langs, _oem, _config, jobId));\n    }\n    return initializeInternal(langs, _oem, _config, jobId);\n  };\n  const setParameters = (params = {}, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'setParameters',\n    payload: {\n      params\n    }\n  }));\n  const recognize = async (image, opts = {}, output = {\n    text: true\n  }, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'recognize',\n    payload: {\n      image: await loadImage(image),\n      options: opts,\n      output\n    }\n  }));\n  const detect = async (image, jobId) => {\n    if (lstmOnlyCore) throw Error('`worker.detect` requires Legacy model, which was not loaded.');\n    return startJob(createJob({\n      id: jobId,\n      action: 'detect',\n      payload: {\n        image: await loadImage(image)\n      }\n    }));\n  };\n  const terminate = async () => {\n    if (worker !== null) {\n      /*\n      await startJob(createJob({\n        id: jobId,\n        action: 'terminate',\n      }));\n      */\n      terminateWorker(worker);\n      worker = null;\n    }\n    return Promise.resolve();\n  };\n  onMessage(worker, ({\n    workerId,\n    jobId,\n    status,\n    action,\n    data\n  }) => {\n    const promiseId = `${action}-${jobId}`;\n    if (status === 'resolve') {\n      log(`[${workerId}]: Complete ${jobId}`);\n      promises[promiseId].resolve({\n        jobId,\n        data\n      });\n      delete promises[promiseId];\n    } else if (status === 'reject') {\n      promises[promiseId].reject(data);\n      delete promises[promiseId];\n      if (action === 'load') workerResReject(data);\n      if (errorHandler) {\n        errorHandler(data);\n      } else {\n        throw Error(data);\n      }\n    } else if (status === 'progress') {\n      logger({\n        ...data,\n        userJobId: jobId\n      });\n    }\n  });\n  const resolveObj = {\n    id,\n    worker,\n    load,\n    writeText,\n    readText,\n    removeFile,\n    FS,\n    reinitialize,\n    setParameters,\n    recognize,\n    detect,\n    terminate\n  };\n  loadInternal().then(() => loadLanguageInternal(langs)).then(() => initializeInternal(langs, oem, config)).then(() => workerResResolve(resolveObj)).catch(() => {});\n  return workerRes;\n};", "map": {"version": 3, "names": ["require", "resolvePaths", "createJob", "log", "getId", "OEM", "defaultOptions", "spawnWorker", "terminateWorker", "onMessage", "loadImage", "send", "workerCounter", "module", "exports", "langs", "oem", "LSTM_ONLY", "_options", "config", "id", "logger", "<PERSON><PERSON><PERSON><PERSON>", "options", "promises", "current<PERSON><PERSON><PERSON>", "split", "currentOem", "currentConfig", "lstmOnlyCore", "DEFAULT", "includes", "legacyCore", "workerResReject", "workerResResolve", "workerRes", "Promise", "resolve", "reject", "workerError", "event", "message", "worker", "onerror", "startJob", "jobId", "action", "payload", "promiseId", "workerId", "load", "console", "warn", "loadInternal", "lstmOnly", "corePath", "logging", "writeText", "path", "text", "method", "args", "readText", "encoding", "removeFile", "FS", "loadLanguageInternal", "_langs", "lang<PERSON><PERSON>", "dataPath", "cachePath", "cacheMethod", "gzip", "legacyLang", "initializeInternal", "_oem", "_config", "reinitialize", "TESSERACT_ONLY", "TESSERACT_LSTM_COMBINED", "Error", "langsArr", "filter", "x", "push", "length", "then", "setParameters", "params", "recognize", "image", "opts", "output", "detect", "terminate", "status", "data", "userJobId", "resolveObj", "catch"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/createWorker.js"], "sourcesContent": ["'use strict';\n\nconst resolvePaths = require('./utils/resolvePaths');\nconst createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\nconst OEM = require('./constants/OEM');\nconst {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  loadImage,\n  send,\n} = require('./worker/node');\n\nlet workerCounter = 0;\n\nmodule.exports = async (langs = 'eng', oem = OEM.LSTM_ONLY, _options = {}, config = {}) => {\n  const id = getId('Worker', workerCounter);\n  const {\n    logger,\n    errorHandler,\n    ...options\n  } = resolvePaths({\n    ...defaultOptions,\n    ..._options,\n  });\n  const promises = {};\n\n  // Current langs, oem, and config file.\n  // Used if the user ever re-initializes the worker using `worker.reinitialize`.\n  const currentLangs = typeof langs === 'string' ? langs.split('+') : langs;\n  let currentOem = oem;\n  let currentConfig = config;\n  const lstmOnlyCore = [OEM.DEFAULT, OEM.LSTM_ONLY].includes(oem) && !options.legacyCore;\n\n  let workerResReject;\n  let workerResResolve;\n  const workerRes = new Promise((resolve, reject) => {\n    workerResResolve = resolve;\n    workerResReject = reject;\n  });\n  const workerError = (event) => { workerResReject(event.message); };\n\n  let worker = spawnWorker(options);\n  worker.onerror = workerError;\n\n  workerCounter += 1;\n\n  const startJob = ({ id: jobId, action, payload }) => (\n    new Promise((resolve, reject) => {\n      log(`[${id}]: Start ${jobId}, action=${action}`);\n      // Using both `action` and `jobId` in case user provides non-unique `jobId`.\n      const promiseId = `${action}-${jobId}`;\n      promises[promiseId] = { resolve, reject };\n      send(worker, {\n        workerId: id,\n        jobId,\n        action,\n        payload,\n      });\n    })\n  );\n\n  const load = () => (\n    console.warn('`load` is depreciated and should be removed from code (workers now come pre-loaded)')\n  );\n\n  const loadInternal = (jobId) => (\n    startJob(createJob({\n      id: jobId, action: 'load', payload: { options: { lstmOnly: lstmOnlyCore, corePath: options.corePath, logging: options.logging } },\n    }))\n  );\n\n  const writeText = (path, text, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'writeFile', args: [path, text] },\n    }))\n  );\n\n  const readText = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'readFile', args: [path, { encoding: 'utf8' }] },\n    }))\n  );\n\n  const removeFile = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'unlink', args: [path] },\n    }))\n  );\n\n  const FS = (method, args, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method, args },\n    }))\n  );\n\n  const loadLanguageInternal = (_langs, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'loadLanguage',\n    payload: {\n      langs: _langs,\n      options: {\n        langPath: options.langPath,\n        dataPath: options.dataPath,\n        cachePath: options.cachePath,\n        cacheMethod: options.cacheMethod,\n        gzip: options.gzip,\n        lstmOnly: [OEM.DEFAULT, OEM.LSTM_ONLY].includes(currentOem)\n          && !options.legacyLang,\n      },\n    },\n  }));\n\n  const initializeInternal = (_langs, _oem, _config, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'initialize',\n      payload: { langs: _langs, oem: _oem, config: _config },\n    }))\n  );\n\n  const reinitialize = (langs = 'eng', oem, config, jobId) => { // eslint-disable-line\n\n    if (lstmOnlyCore && [OEM.TESSERACT_ONLY, OEM.TESSERACT_LSTM_COMBINED].includes(oem)) throw Error('Legacy model requested but code missing.');\n\n    const _oem = oem || currentOem;\n    currentOem = _oem;\n\n    const _config = config || currentConfig;\n    currentConfig = _config;\n\n    // Only load langs that are not already loaded.\n    // This logic fails if the user downloaded the LSTM-only English data for a language\n    // and then uses `worker.reinitialize` to switch to the Legacy engine.\n    // However, the correct data will still be downloaded after initialization fails\n    // and this can be avoided entirely if the user loads the correct data ahead of time.\n    const langsArr = typeof langs === 'string' ? langs.split('+') : langs;\n    const _langs = langsArr.filter((x) => !currentLangs.includes(x));\n    currentLangs.push(..._langs);\n\n    if (_langs.length > 0) {\n      return loadLanguageInternal(_langs, jobId)\n        .then(() => initializeInternal(langs, _oem, _config, jobId));\n    }\n\n    return initializeInternal(langs, _oem, _config, jobId);\n  };\n\n  const setParameters = (params = {}, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'setParameters',\n      payload: { params },\n    }))\n  );\n\n  const recognize = async (image, opts = {}, output = {\n    text: true,\n  }, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'recognize',\n      payload: { image: await loadImage(image), options: opts, output },\n    }))\n  );\n\n  const detect = async (image, jobId) => {\n    if (lstmOnlyCore) throw Error('`worker.detect` requires Legacy model, which was not loaded.');\n\n    return startJob(createJob({\n      id: jobId,\n      action: 'detect',\n      payload: { image: await loadImage(image) },\n    }));\n  };\n\n  const terminate = async () => {\n    if (worker !== null) {\n      /*\n      await startJob(createJob({\n        id: jobId,\n        action: 'terminate',\n      }));\n      */\n      terminateWorker(worker);\n      worker = null;\n    }\n    return Promise.resolve();\n  };\n\n  onMessage(worker, ({\n    workerId, jobId, status, action, data,\n  }) => {\n    const promiseId = `${action}-${jobId}`;\n    if (status === 'resolve') {\n      log(`[${workerId}]: Complete ${jobId}`);\n      promises[promiseId].resolve({ jobId, data });\n      delete promises[promiseId];\n    } else if (status === 'reject') {\n      promises[promiseId].reject(data);\n      delete promises[promiseId];\n      if (action === 'load') workerResReject(data);\n      if (errorHandler) {\n        errorHandler(data);\n      } else {\n        throw Error(data);\n      }\n    } else if (status === 'progress') {\n      logger({ ...data, userJobId: jobId });\n    }\n  });\n\n  const resolveObj = {\n    id,\n    worker,\n    load,\n    writeText,\n    readText,\n    removeFile,\n    FS,\n    reinitialize,\n    setParameters,\n    recognize,\n    detect,\n    terminate,\n  };\n\n  loadInternal()\n    .then(() => loadLanguageInternal(langs))\n    .then(() => initializeInternal(langs, oem, config))\n    .then(() => workerResResolve(resolveObj))\n    .catch(() => {});\n\n  return workerRes;\n};\n"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEb,MAAMC,YAAY,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AACpD,MAAME,SAAS,GAAGF,OAAO,CAAC,aAAa,CAAC;AACxC,MAAM;EAAEG;AAAI,CAAC,GAAGH,OAAO,CAAC,aAAa,CAAC;AACtC,MAAMI,KAAK,GAAGJ,OAAO,CAAC,eAAe,CAAC;AACtC,MAAMK,GAAG,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AACtC,MAAM;EACJM,cAAc;EACdC,WAAW;EACXC,eAAe;EACfC,SAAS;EACTC,SAAS;EACTC;AACF,CAAC,GAAGX,OAAO,CAAC,eAAe,CAAC;AAE5B,IAAIY,aAAa,GAAG,CAAC;AAErBC,MAAM,CAACC,OAAO,GAAG,OAAOC,KAAK,GAAG,KAAK,EAAEC,GAAG,GAAGX,GAAG,CAACY,SAAS,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,KAAK;EACzF,MAAMC,EAAE,GAAGhB,KAAK,CAAC,QAAQ,EAAEQ,aAAa,CAAC;EACzC,MAAM;IACJS,MAAM;IACNC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGtB,YAAY,CAAC;IACf,GAAGK,cAAc;IACjB,GAAGY;EACL,CAAC,CAAC;EACF,MAAMM,QAAQ,GAAG,CAAC,CAAC;;EAEnB;EACA;EACA,MAAMC,YAAY,GAAG,OAAOV,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,GAAGX,KAAK;EACzE,IAAIY,UAAU,GAAGX,GAAG;EACpB,IAAIY,aAAa,GAAGT,MAAM;EAC1B,MAAMU,YAAY,GAAG,CAACxB,GAAG,CAACyB,OAAO,EAAEzB,GAAG,CAACY,SAAS,CAAC,CAACc,QAAQ,CAACf,GAAG,CAAC,IAAI,CAACO,OAAO,CAACS,UAAU;EAEtF,IAAIC,eAAe;EACnB,IAAIC,gBAAgB;EACpB,MAAMC,SAAS,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACjDJ,gBAAgB,GAAGG,OAAO;IAC1BJ,eAAe,GAAGK,MAAM;EAC1B,CAAC,CAAC;EACF,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAAEP,eAAe,CAACO,KAAK,CAACC,OAAO,CAAC;EAAE,CAAC;EAElE,IAAIC,MAAM,GAAGnC,WAAW,CAACgB,OAAO,CAAC;EACjCmB,MAAM,CAACC,OAAO,GAAGJ,WAAW;EAE5B3B,aAAa,IAAI,CAAC;EAElB,MAAMgC,QAAQ,GAAGA,CAAC;IAAExB,EAAE,EAAEyB,KAAK;IAAEC,MAAM;IAAEC;EAAQ,CAAC,KAC9C,IAAIX,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC/BnC,GAAG,CAAC,IAAIiB,EAAE,YAAYyB,KAAK,YAAYC,MAAM,EAAE,CAAC;IAChD;IACA,MAAME,SAAS,GAAG,GAAGF,MAAM,IAAID,KAAK,EAAE;IACtCrB,QAAQ,CAACwB,SAAS,CAAC,GAAG;MAAEX,OAAO;MAAEC;IAAO,CAAC;IACzC3B,IAAI,CAAC+B,MAAM,EAAE;MACXO,QAAQ,EAAE7B,EAAE;MACZyB,KAAK;MACLC,MAAM;MACNC;IACF,CAAC,CAAC;EACJ,CAAC,CACF;EAED,MAAMG,IAAI,GAAGA,CAAA,KACXC,OAAO,CAACC,IAAI,CAAC,qFAAqF,CACnG;EAED,MAAMC,YAAY,GAAIR,KAAK,IACzBD,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IAAEC,MAAM,EAAE,MAAM;IAAEC,OAAO,EAAE;MAAExB,OAAO,EAAE;QAAE+B,QAAQ,EAAEzB,YAAY;QAAE0B,QAAQ,EAAEhC,OAAO,CAACgC,QAAQ;QAAEC,OAAO,EAAEjC,OAAO,CAACiC;MAAQ;IAAE;EAClI,CAAC,CAAC,CACH;EAED,MAAMC,SAAS,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEd,KAAK,KAClCD,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;MAAEa,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,CAACH,IAAI,EAAEC,IAAI;IAAE;EACrD,CAAC,CAAC,CACH;EAED,MAAMG,QAAQ,GAAGA,CAACJ,IAAI,EAAEb,KAAK,KAC3BD,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;MAAEa,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,CAACH,IAAI,EAAE;QAAEK,QAAQ,EAAE;MAAO,CAAC;IAAE;EACpE,CAAC,CAAC,CACH;EAED,MAAMC,UAAU,GAAGA,CAACN,IAAI,EAAEb,KAAK,KAC7BD,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;MAAEa,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAACH,IAAI;IAAE;EAC5C,CAAC,CAAC,CACH;EAED,MAAMO,EAAE,GAAGA,CAACL,MAAM,EAAEC,IAAI,EAAEhB,KAAK,KAC7BD,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;MAAEa,MAAM;MAAEC;IAAK;EAC1B,CAAC,CAAC,CACH;EAED,MAAMK,oBAAoB,GAAGA,CAACC,MAAM,EAAEtB,KAAK,KAAKD,QAAQ,CAAC1C,SAAS,CAAC;IACjEkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,cAAc;IACtBC,OAAO,EAAE;MACPhC,KAAK,EAAEoD,MAAM;MACb5C,OAAO,EAAE;QACP6C,QAAQ,EAAE7C,OAAO,CAAC6C,QAAQ;QAC1BC,QAAQ,EAAE9C,OAAO,CAAC8C,QAAQ;QAC1BC,SAAS,EAAE/C,OAAO,CAAC+C,SAAS;QAC5BC,WAAW,EAAEhD,OAAO,CAACgD,WAAW;QAChCC,IAAI,EAAEjD,OAAO,CAACiD,IAAI;QAClBlB,QAAQ,EAAE,CAACjD,GAAG,CAACyB,OAAO,EAAEzB,GAAG,CAACY,SAAS,CAAC,CAACc,QAAQ,CAACJ,UAAU,CAAC,IACtD,CAACJ,OAAO,CAACkD;MAChB;IACF;EACF,CAAC,CAAC,CAAC;EAEH,MAAMC,kBAAkB,GAAGA,CAACP,MAAM,EAAEQ,IAAI,EAAEC,OAAO,EAAE/B,KAAK,KACtDD,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,YAAY;IACpBC,OAAO,EAAE;MAAEhC,KAAK,EAAEoD,MAAM;MAAEnD,GAAG,EAAE2D,IAAI;MAAExD,MAAM,EAAEyD;IAAQ;EACvD,CAAC,CAAC,CACH;EAED,MAAMC,YAAY,GAAGA,CAAC9D,KAAK,GAAG,KAAK,EAAEC,GAAG,EAAEG,MAAM,EAAE0B,KAAK,KAAK;IAAE;;IAE5D,IAAIhB,YAAY,IAAI,CAACxB,GAAG,CAACyE,cAAc,EAAEzE,GAAG,CAAC0E,uBAAuB,CAAC,CAAChD,QAAQ,CAACf,GAAG,CAAC,EAAE,MAAMgE,KAAK,CAAC,0CAA0C,CAAC;IAE5I,MAAML,IAAI,GAAG3D,GAAG,IAAIW,UAAU;IAC9BA,UAAU,GAAGgD,IAAI;IAEjB,MAAMC,OAAO,GAAGzD,MAAM,IAAIS,aAAa;IACvCA,aAAa,GAAGgD,OAAO;;IAEvB;IACA;IACA;IACA;IACA;IACA,MAAMK,QAAQ,GAAG,OAAOlE,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,GAAGX,KAAK;IACrE,MAAMoD,MAAM,GAAGc,QAAQ,CAACC,MAAM,CAAEC,CAAC,IAAK,CAAC1D,YAAY,CAACM,QAAQ,CAACoD,CAAC,CAAC,CAAC;IAChE1D,YAAY,CAAC2D,IAAI,CAAC,GAAGjB,MAAM,CAAC;IAE5B,IAAIA,MAAM,CAACkB,MAAM,GAAG,CAAC,EAAE;MACrB,OAAOnB,oBAAoB,CAACC,MAAM,EAAEtB,KAAK,CAAC,CACvCyC,IAAI,CAAC,MAAMZ,kBAAkB,CAAC3D,KAAK,EAAE4D,IAAI,EAAEC,OAAO,EAAE/B,KAAK,CAAC,CAAC;IAChE;IAEA,OAAO6B,kBAAkB,CAAC3D,KAAK,EAAE4D,IAAI,EAAEC,OAAO,EAAE/B,KAAK,CAAC;EACxD,CAAC;EAED,MAAM0C,aAAa,GAAGA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE3C,KAAK,KACvCD,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,eAAe;IACvBC,OAAO,EAAE;MAAEyC;IAAO;EACpB,CAAC,CAAC,CACH;EAED,MAAMC,SAAS,GAAG,MAAAA,CAAOC,KAAK,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAG;IAClDjC,IAAI,EAAE;EACR,CAAC,EAAEd,KAAK,KACND,QAAQ,CAAC1C,SAAS,CAAC;IACjBkB,EAAE,EAAEyB,KAAK;IACTC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE;MAAE2C,KAAK,EAAE,MAAMhF,SAAS,CAACgF,KAAK,CAAC;MAAEnE,OAAO,EAAEoE,IAAI;MAAEC;IAAO;EAClE,CAAC,CAAC,CACH;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAOH,KAAK,EAAE7C,KAAK,KAAK;IACrC,IAAIhB,YAAY,EAAE,MAAMmD,KAAK,CAAC,8DAA8D,CAAC;IAE7F,OAAOpC,QAAQ,CAAC1C,SAAS,CAAC;MACxBkB,EAAE,EAAEyB,KAAK;MACTC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE;QAAE2C,KAAK,EAAE,MAAMhF,SAAS,CAACgF,KAAK;MAAE;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIpD,MAAM,KAAK,IAAI,EAAE;MACnB;AACN;AACA;AACA;AACA;AACA;MACMlC,eAAe,CAACkC,MAAM,CAAC;MACvBA,MAAM,GAAG,IAAI;IACf;IACA,OAAON,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B,CAAC;EAED5B,SAAS,CAACiC,MAAM,EAAE,CAAC;IACjBO,QAAQ;IAAEJ,KAAK;IAAEkD,MAAM;IAAEjD,MAAM;IAAEkD;EACnC,CAAC,KAAK;IACJ,MAAMhD,SAAS,GAAG,GAAGF,MAAM,IAAID,KAAK,EAAE;IACtC,IAAIkD,MAAM,KAAK,SAAS,EAAE;MACxB5F,GAAG,CAAC,IAAI8C,QAAQ,eAAeJ,KAAK,EAAE,CAAC;MACvCrB,QAAQ,CAACwB,SAAS,CAAC,CAACX,OAAO,CAAC;QAAEQ,KAAK;QAAEmD;MAAK,CAAC,CAAC;MAC5C,OAAOxE,QAAQ,CAACwB,SAAS,CAAC;IAC5B,CAAC,MAAM,IAAI+C,MAAM,KAAK,QAAQ,EAAE;MAC9BvE,QAAQ,CAACwB,SAAS,CAAC,CAACV,MAAM,CAAC0D,IAAI,CAAC;MAChC,OAAOxE,QAAQ,CAACwB,SAAS,CAAC;MAC1B,IAAIF,MAAM,KAAK,MAAM,EAAEb,eAAe,CAAC+D,IAAI,CAAC;MAC5C,IAAI1E,YAAY,EAAE;QAChBA,YAAY,CAAC0E,IAAI,CAAC;MACpB,CAAC,MAAM;QACL,MAAMhB,KAAK,CAACgB,IAAI,CAAC;MACnB;IACF,CAAC,MAAM,IAAID,MAAM,KAAK,UAAU,EAAE;MAChC1E,MAAM,CAAC;QAAE,GAAG2E,IAAI;QAAEC,SAAS,EAAEpD;MAAM,CAAC,CAAC;IACvC;EACF,CAAC,CAAC;EAEF,MAAMqD,UAAU,GAAG;IACjB9E,EAAE;IACFsB,MAAM;IACNQ,IAAI;IACJO,SAAS;IACTK,QAAQ;IACRE,UAAU;IACVC,EAAE;IACFY,YAAY;IACZU,aAAa;IACbE,SAAS;IACTI,MAAM;IACNC;EACF,CAAC;EAEDzC,YAAY,CAAC,CAAC,CACXiC,IAAI,CAAC,MAAMpB,oBAAoB,CAACnD,KAAK,CAAC,CAAC,CACvCuE,IAAI,CAAC,MAAMZ,kBAAkB,CAAC3D,KAAK,EAAEC,GAAG,EAAEG,MAAM,CAAC,CAAC,CAClDmE,IAAI,CAAC,MAAMpD,gBAAgB,CAACgE,UAAU,CAAC,CAAC,CACxCC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;EAElB,OAAOhE,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}