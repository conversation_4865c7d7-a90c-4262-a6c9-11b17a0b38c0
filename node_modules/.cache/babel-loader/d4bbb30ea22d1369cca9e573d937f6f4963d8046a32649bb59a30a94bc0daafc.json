{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nconst createJob = require('./createJob');\nconst {\n  log\n} = require('./utils/log');\nconst getId = require('./utils/getId');\nlet schedulerCounter = 0;\nmodule.exports = () => {\n  const id = getId('Scheduler', schedulerCounter);\n  const workers = {};\n  const runningWorkers = {};\n  let jobQueue = [];\n  schedulerCounter += 1;\n  const getQueueLen = () => jobQueue.length;\n  const getNumWorkers = () => Object.keys(workers).length;\n  const dequeue = () => {\n    if (jobQueue.length !== 0) {\n      const wIds = Object.keys(workers);\n      for (let i = 0; i < wIds.length; i += 1) {\n        if (typeof runningWorkers[wIds[i]] === 'undefined') {\n          jobQueue[0](workers[wIds[i]]);\n          break;\n        }\n      }\n    }\n  };\n  const queue = (action, payload) => new Promise((resolve, reject) => {\n    const job = createJob({\n      action,\n      payload\n    });\n    jobQueue.push(async w => {\n      jobQueue.shift();\n      runningWorkers[w.id] = job;\n      try {\n        resolve(await w[action].apply(this, [...payload, job.id]));\n      } catch (err) {\n        reject(err);\n      } finally {\n        delete runningWorkers[w.id];\n        dequeue();\n      }\n    });\n    log(`[${id}]: Add ${job.id} to JobQueue`);\n    log(`[${id}]: JobQueue length=${jobQueue.length}`);\n    dequeue();\n  });\n  const addWorker = w => {\n    workers[w.id] = w;\n    log(`[${id}]: Add ${w.id}`);\n    log(`[${id}]: Number of workers=${getNumWorkers()}`);\n    dequeue();\n    return w.id;\n  };\n  const addJob = async (action, ...payload) => {\n    if (getNumWorkers() === 0) {\n      throw Error(`[${id}]: You need to have at least one worker before adding jobs`);\n    }\n    return queue(action, payload);\n  };\n  const terminate = async () => {\n    Object.keys(workers).forEach(async wid => {\n      await workers[wid].terminate();\n    });\n    jobQueue = [];\n  };\n  return {\n    addWorker,\n    addJob,\n    terminate,\n    getQueueLen,\n    getNumWorkers\n  };\n};", "map": {"version": 3, "names": ["require", "createJob", "log", "getId", "schedulerCounter", "module", "exports", "id", "workers", "runningWorkers", "jobQueue", "getQueueLen", "length", "getNumWorkers", "Object", "keys", "dequeue", "wIds", "i", "queue", "action", "payload", "Promise", "resolve", "reject", "job", "push", "w", "shift", "apply", "err", "addWorker", "addJob", "Error", "terminate", "for<PERSON>ach", "wid"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/createScheduler.js"], "sourcesContent": ["'use strict';\n\nconst createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\n\nlet schedulerCounter = 0;\n\nmodule.exports = () => {\n  const id = getId('Scheduler', schedulerCounter);\n  const workers = {};\n  const runningWorkers = {};\n  let jobQueue = [];\n\n  schedulerCounter += 1;\n\n  const getQueueLen = () => jobQueue.length;\n  const getNumWorkers = () => Object.keys(workers).length;\n\n  const dequeue = () => {\n    if (jobQueue.length !== 0) {\n      const wIds = Object.keys(workers);\n      for (let i = 0; i < wIds.length; i += 1) {\n        if (typeof runningWorkers[wIds[i]] === 'undefined') {\n          jobQueue[0](workers[wIds[i]]);\n          break;\n        }\n      }\n    }\n  };\n\n  const queue = (action, payload) => (\n    new Promise((resolve, reject) => {\n      const job = createJob({ action, payload });\n      jobQueue.push(async (w) => {\n        jobQueue.shift();\n        runningWorkers[w.id] = job;\n        try {\n          resolve(await w[action].apply(this, [...payload, job.id]));\n        } catch (err) {\n          reject(err);\n        } finally {\n          delete runningWorkers[w.id];\n          dequeue();\n        }\n      });\n      log(`[${id}]: Add ${job.id} to JobQueue`);\n      log(`[${id}]: JobQueue length=${jobQueue.length}`);\n      dequeue();\n    })\n  );\n\n  const addWorker = (w) => {\n    workers[w.id] = w;\n    log(`[${id}]: Add ${w.id}`);\n    log(`[${id}]: Number of workers=${getNumWorkers()}`);\n    dequeue();\n    return w.id;\n  };\n\n  const addJob = async (action, ...payload) => {\n    if (getNumWorkers() === 0) {\n      throw Error(`[${id}]: You need to have at least one worker before adding jobs`);\n    }\n    return queue(action, payload);\n  };\n\n  const terminate = async () => {\n    Object.keys(workers).forEach(async (wid) => {\n      await workers[wid].terminate();\n    });\n    jobQueue = [];\n  };\n\n  return {\n    addWorker,\n    addJob,\n    terminate,\n    getQueueLen,\n    getNumWorkers,\n  };\n};\n"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEb,MAAMC,SAAS,GAAGD,OAAO,CAAC,aAAa,CAAC;AACxC,MAAM;EAAEE;AAAI,CAAC,GAAGF,OAAO,CAAC,aAAa,CAAC;AACtC,MAAMG,KAAK,GAAGH,OAAO,CAAC,eAAe,CAAC;AAEtC,IAAII,gBAAgB,GAAG,CAAC;AAExBC,MAAM,CAACC,OAAO,GAAG,MAAM;EACrB,MAAMC,EAAE,GAAGJ,KAAK,CAAC,WAAW,EAAEC,gBAAgB,CAAC;EAC/C,MAAMI,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMC,cAAc,GAAG,CAAC,CAAC;EACzB,IAAIC,QAAQ,GAAG,EAAE;EAEjBN,gBAAgB,IAAI,CAAC;EAErB,MAAMO,WAAW,GAAGA,CAAA,KAAMD,QAAQ,CAACE,MAAM;EACzC,MAAMC,aAAa,GAAGA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAACI,MAAM;EAEvD,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIN,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;MACzB,MAAMK,IAAI,GAAGH,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC;MACjC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACL,MAAM,EAAEM,CAAC,IAAI,CAAC,EAAE;QACvC,IAAI,OAAOT,cAAc,CAACQ,IAAI,CAACC,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;UAClDR,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO,CAACS,IAAI,CAACC,CAAC,CAAC,CAAC,CAAC;UAC7B;QACF;MACF;IACF;EACF,CAAC;EAED,MAAMC,KAAK,GAAGA,CAACC,MAAM,EAAEC,OAAO,KAC5B,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC/B,MAAMC,GAAG,GAAGxB,SAAS,CAAC;MAAEmB,MAAM;MAAEC;IAAQ,CAAC,CAAC;IAC1CX,QAAQ,CAACgB,IAAI,CAAC,MAAOC,CAAC,IAAK;MACzBjB,QAAQ,CAACkB,KAAK,CAAC,CAAC;MAChBnB,cAAc,CAACkB,CAAC,CAACpB,EAAE,CAAC,GAAGkB,GAAG;MAC1B,IAAI;QACFF,OAAO,CAAC,MAAMI,CAAC,CAACP,MAAM,CAAC,CAACS,KAAK,CAAC,IAAI,EAAE,CAAC,GAAGR,OAAO,EAAEI,GAAG,CAAClB,EAAE,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,OAAOuB,GAAG,EAAE;QACZN,MAAM,CAACM,GAAG,CAAC;MACb,CAAC,SAAS;QACR,OAAOrB,cAAc,CAACkB,CAAC,CAACpB,EAAE,CAAC;QAC3BS,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;IACFd,GAAG,CAAC,IAAIK,EAAE,UAAUkB,GAAG,CAAClB,EAAE,cAAc,CAAC;IACzCL,GAAG,CAAC,IAAIK,EAAE,sBAAsBG,QAAQ,CAACE,MAAM,EAAE,CAAC;IAClDI,OAAO,CAAC,CAAC;EACX,CAAC,CACF;EAED,MAAMe,SAAS,GAAIJ,CAAC,IAAK;IACvBnB,OAAO,CAACmB,CAAC,CAACpB,EAAE,CAAC,GAAGoB,CAAC;IACjBzB,GAAG,CAAC,IAAIK,EAAE,UAAUoB,CAAC,CAACpB,EAAE,EAAE,CAAC;IAC3BL,GAAG,CAAC,IAAIK,EAAE,wBAAwBM,aAAa,CAAC,CAAC,EAAE,CAAC;IACpDG,OAAO,CAAC,CAAC;IACT,OAAOW,CAAC,CAACpB,EAAE;EACb,CAAC;EAED,MAAMyB,MAAM,GAAG,MAAAA,CAAOZ,MAAM,EAAE,GAAGC,OAAO,KAAK;IAC3C,IAAIR,aAAa,CAAC,CAAC,KAAK,CAAC,EAAE;MACzB,MAAMoB,KAAK,CAAC,IAAI1B,EAAE,4DAA4D,CAAC;IACjF;IACA,OAAOY,KAAK,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC/B,CAAC;EAED,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BpB,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAAC2B,OAAO,CAAC,MAAOC,GAAG,IAAK;MAC1C,MAAM5B,OAAO,CAAC4B,GAAG,CAAC,CAACF,SAAS,CAAC,CAAC;IAChC,CAAC,CAAC;IACFxB,QAAQ,GAAG,EAAE;EACf,CAAC;EAED,OAAO;IACLqB,SAAS;IACTC,MAAM;IACNE,SAAS;IACTvB,WAAW;IACXE;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}