{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, computed, onMounted, watch, nextTick } from 'vue';\n\n// 引入Tesseract.js OCR库\nimport Tesseract from 'tesseract.js';\n\n// ==================== 响应式数据定义 ====================\n\n// 词语库 - 包含要练习的中文词语及其拼音和发音\n\nexport default {\n  __name: 'App',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n\n    // 引入Vue 3 Composition API\n    const words = ref([{\n      text: '你好',\n      pinyin: 'nǐ hǎo',\n      audio: 'https://dict.youdao.com/dictvoice?audio=nihao&type=1'\n    }, {\n      text: '谢谢',\n      pinyin: 'xiè xie',\n      audio: 'https://dict.youdao.com/dictvoice?audio=xiexie&type=1'\n    }, {\n      text: '再见',\n      pinyin: 'zài jiàn',\n      audio: 'https://dict.youdao.com/dictvoice?audio=zaijian&type=1'\n    }, {\n      text: '朋友',\n      pinyin: 'péng yǒu',\n      audio: 'https://dict.youdao.com/dictvoice?audio=pengyou&type=1'\n    }, {\n      text: '家人',\n      pinyin: 'jiā rén',\n      audio: 'https://dict.youdao.com/dictvoice?audio=jiaren&type=1'\n    }, {\n      text: '学习',\n      pinyin: 'xué xí',\n      audio: 'https://dict.youdao.com/dictvoice?audio=xuexi&type=1'\n    }, {\n      text: '工作',\n      pinyin: 'gōng zuò',\n      audio: 'https://dict.youdao.com/dictvoice?audio=gongzuo&type=1'\n    }, {\n      text: '时间',\n      pinyin: 'shí jiān',\n      audio: 'https://dict.youdao.com/dictvoice?audio=shijian&type=1'\n    }]);\n\n    // 基础状态数据\n    const currentIndex = ref(0); // 当前词语的索引\n    const recognizedText = ref(''); // OCR识别的文本结果\n    const feedback = ref(null); // 反馈信息对象\n    const isDrawing = ref(false); // 是否正在绘制\n    const isRecognizing = ref(false); // 是否正在进行OCR识别\n\n    // Canvas相关数据\n    const canvas = ref(null); // Canvas DOM引用\n    let ctx = null; // Canvas 2D绘图上下文\n    const gridSize = ref(120); // 每个田字格的大小（像素）\n    const spacing = ref(15); // 田字格之间的间距（像素）\n    const lastX = ref(0); // 上一个绘制点的X坐标\n    const lastY = ref(0); // 上一个绘制点的Y坐标\n\n    // 绘制历史和状态\n    const strokeHistory = ref([]); // 存储每一笔的画布状态，用于撤销功能\n    const currentStroke = ref([]); // 当前正在绘制的笔画点集合\n    const charResults = ref([]); // 每个字符的识别结果数组\n    const recognitionProgress = ref({}); // 识别进度状态对象\n    const debugImages = ref([]); // 调试用的提取图像数组\n    const currentDrawingGrid = ref(-1); // 当前正在绘制的格子索引\n\n    // ==================== 计算属性 ====================\n\n    // 获取当前词语对象\n    const currentWord = computed(() => {\n      return words.value[currentIndex.value];\n    });\n\n    // 计算画布宽度 - 根据字符数量、格子大小和间距计算\n    const canvasWidth = computed(() => {\n      return currentWord.value.text.length * gridSize.value + (currentWord.value.text.length - 1) * spacing.value;\n    });\n\n    // 计算画布高度 - 等于单个格子的高度\n    const canvasHeight = computed(() => {\n      return gridSize.value;\n    });\n\n    // 检查是否所有字符都识别正确\n    const allCharsCompleted = computed(() => {\n      return charResults.value.length === currentWord.value.text.length && charResults.value.every(result => result && result.isCorrect);\n    });\n\n    // ==================== 方法定义 ====================\n\n    // 设置高DPI支持 - 确保在高分辨率屏幕上显示清晰\n    const setupHighDPI = () => {\n      const canvasEl = canvas.value;\n      const dpr = window.devicePixelRatio || 1; // 获取设备像素比\n\n      // 设置画布的实际像素大小\n      canvasEl.width = canvasWidth.value * dpr;\n      canvasEl.height = canvasHeight.value * dpr;\n\n      // 设置画布的CSS显示大小\n      canvasEl.style.width = canvasWidth.value + 'px';\n      canvasEl.style.height = canvasHeight.value + 'px';\n\n      // 缩放绘图上下文以匹配设备像素比\n      ctx.scale(dpr, dpr);\n    };\n\n    // 绘制田字格背景\n    const drawTianziGrid = () => {\n      // 清除整个画布\n      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);\n\n      // 保存当前绘图状态\n      ctx.save();\n\n      // 为每个字符绘制田字格\n      for (let i = 0; i < currentWord.value.text.length; i++) {\n        const x = i * (gridSize.value + spacing.value); // 计算当前格子的X坐标\n\n        // 绘制外框 - 使用较粗的线条\n        ctx.strokeStyle = '#999999';\n        ctx.lineWidth = 2;\n        ctx.strokeRect(x, 0, gridSize.value, gridSize.value);\n\n        // 绘制内部辅助线 - 使用细线\n        ctx.strokeStyle = '#dddddd';\n        ctx.lineWidth = 1;\n\n        // 绘制垂直中线\n        ctx.beginPath();\n        ctx.moveTo(x + gridSize.value / 2, 0);\n        ctx.lineTo(x + gridSize.value / 2, gridSize.value);\n        ctx.stroke();\n\n        // 绘制水平中线\n        ctx.beginPath();\n        ctx.moveTo(x, gridSize.value / 2);\n        ctx.lineTo(x + gridSize.value, gridSize.value / 2);\n        ctx.stroke();\n\n        // 绘制对角线 - 使用更淡的颜色\n        ctx.strokeStyle = '#eeeeee';\n        ctx.lineWidth = 0.5;\n\n        // 左上到右下对角线\n        ctx.beginPath();\n        ctx.moveTo(x, 0);\n        ctx.lineTo(x + gridSize.value, gridSize.value);\n        ctx.stroke();\n\n        // 右上到左下对角线\n        ctx.beginPath();\n        ctx.moveTo(x + gridSize.value, 0);\n        ctx.lineTo(x, gridSize.value);\n        ctx.stroke();\n      }\n\n      // 恢复绘图状态，为手写做准备\n      ctx.restore();\n      ctx.lineWidth = 3;\n      ctx.lineCap = 'round';\n      ctx.lineJoin = 'round';\n      ctx.strokeStyle = '#000000';\n      ctx.fillStyle = '#000000';\n    };\n\n    // 保存当前画布状态到历史记录（用于撤销功能）\n    const saveCanvasState = () => {\n      const imageData = ctx.getImageData(0, 0, canvas.value.width, canvas.value.height);\n      strokeHistory.value.push(imageData);\n\n      // 限制历史记录数量，避免内存过多占用\n      if (strokeHistory.value.length > 20) {\n        strokeHistory.value.shift(); // 删除最早的记录\n      }\n    };\n\n    // 播放当前词语的发音\n    const playPronunciation = () => {\n      const audio = new Audio(currentWord.value.audio);\n      audio.play().catch(error => {\n        console.error('播放发音失败:', error);\n      });\n    };\n\n    // 获取事件在画布中的坐标位置\n    const getEventPos = e => {\n      const rect = canvas.value.getBoundingClientRect();\n      const clientX = e.clientX || e.touches && e.touches[0].clientX;\n      const clientY = e.clientY || e.touches && e.touches[0].clientY;\n      return {\n        x: (clientX - rect.left) * (canvas.value.width / rect.width) / (window.devicePixelRatio || 1),\n        y: (clientY - rect.top) * (canvas.value.height / rect.height) / (window.devicePixelRatio || 1)\n      };\n    };\n\n    // 获取坐标所在的格子索引\n    const getGridIndex = (x, y) => {\n      for (let i = 0; i < currentWord.value.text.length; i++) {\n        const gridX = i * (gridSize.value + spacing.value);\n        if (x >= gridX && x <= gridX + gridSize.value && y >= 0 && y <= gridSize.value) {\n          return i; // 返回格子索引\n        }\n      }\n      return -1; // 不在任何格子内\n    };\n\n    // 开始绘制 - 鼠标按下或触摸开始时调用\n    const startDrawing = e => {\n      if (isRecognizing.value) return; // 识别中时禁止绘制\n\n      const pos = getEventPos(e);\n\n      // 检查是否在任何格子内\n      const gridIndex = getGridIndex(pos.x, pos.y);\n      if (gridIndex === -1) return; // 不在格子内则不开始绘制\n\n      // 记录当前绘制的格子索引\n      currentDrawingGrid.value = gridIndex;\n\n      // 在开始新的笔画前保存当前状态\n      saveCanvasState();\n      isDrawing.value = true;\n      lastX.value = pos.x;\n      lastY.value = pos.y;\n      currentStroke.value = [pos];\n      ctx.beginPath();\n      ctx.moveTo(pos.x, pos.y);\n    };\n\n    // 绘制过程 - 鼠标移动或触摸移动时调用\n    const draw = e => {\n      if (!isDrawing.value || isRecognizing.value) return;\n      const pos = getEventPos(e);\n\n      // 检查是否还在同一个格子内\n      const gridIndex = getGridIndex(pos.x, pos.y);\n      if (gridIndex !== currentDrawingGrid.value) {\n        stopDrawing(); // 离开当前格子则停止绘制\n        return;\n      }\n\n      // 记录当前笔画的点\n      currentStroke.value.push(pos);\n\n      // 使用二次贝塞尔曲线使线条更平滑\n      ctx.quadraticCurveTo(lastX.value, lastY.value, (pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2);\n      ctx.stroke();\n      ctx.beginPath();\n      ctx.moveTo((pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2);\n      lastX.value = pos.x;\n      lastY.value = pos.y;\n    };\n\n    // 停止绘制 - 鼠标抬起或触摸结束时调用\n    const stopDrawing = () => {\n      if (isDrawing.value) {\n        isDrawing.value = false;\n        ctx.closePath();\n        currentStroke.value = []; // 清空当前笔画\n        currentDrawingGrid.value = -1; // 重置当前绘制格子\n      }\n    };\n\n    // 重置词语状态 - 切换到新词语时调用\n    const resetWordState = () => {\n      charResults.value = [];\n      recognitionProgress.value = {};\n      debugImages.value = [];\n      clearCanvas();\n    };\n\n    // 清除画布\n    const clearCanvas = () => {\n      drawTianziGrid(); // 清除后重新绘制田字格\n      strokeHistory.value = []; // 清空撤销历史\n      currentStroke.value = [];\n      recognizedText.value = '';\n      feedback.value = null;\n    };\n\n    // 撤销上一笔绘制\n    const undoLastStroke = () => {\n      if (strokeHistory.value.length === 0) return;\n\n      // 恢复到上一个状态\n      const lastState = strokeHistory.value.pop();\n      ctx.putImageData(lastState, 0, 0);\n      recognizedText.value = '';\n      feedback.value = null;\n    };\n\n    // 获取字符状态的CSS类名\n    const getCharStatusClass = index => {\n      if (recognitionProgress.value[index] === 'recognizing') return 'current';\n      if (charResults.value[index]) {\n        return charResults.value[index].isCorrect ? 'correct' : 'incorrect';\n      }\n      return 'pending';\n    };\n\n    // 获取字符状态的显示文本\n    const getCharStatusText = index => {\n      if (recognitionProgress.value[index] === 'recognizing') return '识别中';\n      if (charResults.value[index]) {\n        return charResults.value[index].isCorrect ? '正确' : '错误';\n      }\n      return '待识别';\n    };\n\n    // 显示调试图像 - 提取每个字符的图像用于调试\n    const showDebugImages = () => {\n      debugImages.value = [];\n\n      // 先在原canvas上绘制提取区域边界（用于调试）\n      drawExtractionBounds();\n      for (let i = 0; i < currentWord.value.text.length; i++) {\n        const charCanvas = extractCharCanvasAtIndex(i);\n\n        // 保存图像数据\n        debugImages.value.push({\n          canvas: charCanvas,\n          width: charCanvas.width,\n          height: charCanvas.height,\n          imageData: charCanvas.getContext('2d').getImageData(0, 0, charCanvas.width, charCanvas.height)\n        });\n      }\n\n      // 等待Vue更新DOM后绘制调试图像\n      nextTick(() => {\n        renderDebugImages();\n      });\n    };\n\n    // 绘制提取区域边界 - 用于调试\n    const drawExtractionBounds = () => {\n      ctx.save();\n      ctx.strokeStyle = 'red';\n      ctx.lineWidth = 2;\n      ctx.setLineDash([5, 5]); // 虚线样式\n\n      for (let i = 0; i < currentWord.value.text.length; i++) {\n        const x = i * (gridSize.value + spacing.value);\n        ctx.strokeRect(x, 0, gridSize.value, gridSize.value);\n      }\n      ctx.restore();\n\n      // 2秒后清除边界线\n      setTimeout(() => {\n        drawTianziGrid();\n      }, 2000);\n    };\n\n    // 渲染调试图像到调试面板\n    const renderDebugImages = () => {\n      debugImages.value.forEach((debugImg, index) => {\n        const canvasRef = document.querySelector(`[data-ref=\"debugCanvas${index}\"]`);\n        if (canvasRef) {\n          const ctx = canvasRef.getContext('2d');\n\n          // 清除画布\n          ctx.clearRect(0, 0, canvasRef.width, canvasRef.height);\n\n          // 绘制提取的图像\n          ctx.putImageData(debugImg.imageData, 0, 0);\n        }\n      });\n    };\n\n    // 提取指定索引位置的字符画布\n    const extractCharCanvasAtIndex = index => {\n      // 创建临时canvas，只包含指定字符格子的内容\n      const tempCanvas = document.createElement('canvas');\n      const tempCtx = tempCanvas.getContext('2d');\n\n      // 获取设备像素比\n      const dpr = window.devicePixelRatio || 1;\n\n      // 设置临时canvas大小（考虑DPI）\n      tempCanvas.width = gridSize.value * dpr;\n      tempCanvas.height = gridSize.value * dpr;\n\n      // 设置CSS显示大小\n      tempCanvas.style.width = gridSize.value + 'px';\n      tempCanvas.style.height = gridSize.value + 'px';\n\n      // 缩放绘图上下文\n      tempCtx.scale(dpr, dpr);\n\n      // 填充白色背景\n      tempCtx.fillStyle = 'white';\n      tempCtx.fillRect(0, 0, gridSize.value, gridSize.value);\n\n      // 计算指定格子在显示坐标系中的位置（不考虑DPI）\n      const displaySourceX = index * (gridSize.value + spacing.value);\n      const displaySourceY = 0;\n      const displayWidth = gridSize.value;\n      const displayHeight = gridSize.value;\n\n      // 计算在实际canvas像素中的位置（考虑DPI）\n      const actualSourceX = displaySourceX * dpr;\n      const actualSourceY = displaySourceY * dpr;\n      const actualWidth = displayWidth * dpr;\n      const actualHeight = displayHeight * dpr;\n      console.log(`提取字符${index + 1}:`, {\n        index: index,\n        displaySourceX: displaySourceX,\n        displaySourceY: displaySourceY,\n        displayWidth: displayWidth,\n        displayHeight: displayHeight,\n        actualSourceX: actualSourceX,\n        actualSourceY: actualSourceY,\n        actualWidth: actualWidth,\n        actualHeight: actualHeight,\n        dpr: dpr,\n        canvasActualWidth: canvas.value.width,\n        canvasActualHeight: canvas.value.height,\n        canvasDisplayWidth: canvas.value.style.width,\n        canvasDisplayHeight: canvas.value.style.height\n      });\n\n      // 从原canvas中提取指定格子的内容\n      // 注意：这里使用显示坐标系，因为tempCtx已经缩放了\n      tempCtx.drawImage(canvas.value, actualSourceX, actualSourceY, actualWidth, actualHeight,\n      // 源区域（实际像素）\n      0, 0, displayWidth, displayHeight // 目标区域（显示坐标）\n      );\n      return tempCanvas;\n    };\n\n    // 识别所有字符 - 主要的OCR识别函数\n    const recognizeAllChars = async () => {\n      if (isRecognizing.value) return;\n      isRecognizing.value = true;\n      recognizedText.value = '';\n      feedback.value = null;\n      charResults.value = [];\n      recognitionProgress.value = {};\n\n      // 先显示调试图像\n      showDebugImages();\n      try {\n        // 顺序识别每个字符（一个一个提交OCR）\n        for (let i = 0; i < currentWord.value.text.length; i++) {\n          recognitionProgress.value[i] = 'recognizing';\n          try {\n            console.log(`开始识别第${i + 1}个字符: ${currentWord.value.text[i]}`);\n            const result = await recognizeCharAtIndex(i);\n            charResults.value[i] = result;\n            recognitionProgress.value[i] = 'completed';\n            console.log(`第${i + 1}个字符识别完成:`, result);\n\n            // 短暂延迟，让用户看到进度\n            await new Promise(resolve => setTimeout(resolve, 500));\n          } catch (error) {\n            console.error(`第${i + 1}个字符识别失败:`, error);\n            charResults.value[i] = {\n              recognized: '',\n              expected: currentWord.value.text[i],\n              isCorrect: false\n            };\n            recognitionProgress.value[i] = 'completed';\n          }\n        }\n\n        // 处理最终结果\n        const correctCount = charResults.value.filter(r => r.isCorrect).length;\n        const totalCount = charResults.value.length;\n        const allCorrect = correctCount === totalCount;\n        let resultMessage = '';\n        if (allCorrect) {\n          resultMessage = `全部正确！识别结果：${charResults.value.map(r => r.recognized).join('')}`;\n        } else {\n          resultMessage = `识别完成：${correctCount}/${totalCount} 正确`;\n          // 显示详细结果\n          const details = charResults.value.map((r, i) => `${currentWord.value.text[i]}→${r.recognized}${r.isCorrect ? '✓' : '✗'}`).join(' ');\n          resultMessage += `\\n详情：${details}`;\n        }\n        feedback.value = {\n          isCorrect: allCorrect,\n          message: resultMessage\n        };\n        if (allCorrect) {\n          setTimeout(() => {\n            feedback.value = {\n              isCorrect: true,\n              message: '恭喜！可以进入下一个词语了'\n            };\n          }, 2000);\n        }\n      } catch (error) {\n        console.error('识别失败:', error);\n        feedback.value = {\n          isCorrect: false,\n          message: '识别失败，请重试'\n        };\n      } finally {\n        isRecognizing.value = false;\n      }\n    };\n\n    // 识别指定索引位置的字符\n    const recognizeCharAtIndex = async index => {\n      try {\n        // 创建只包含指定格子的canvas\n        const charCanvas = extractCharCanvasAtIndex(index);\n        const result = await Tesseract.recognize(charCanvas, 'chi_sim',\n        // 简体中文\n        {\n          logger: m => {\n            if (m.status === 'recognizing text') {\n              console.log(`字符${index + 1}识别进度: ${Math.round(m.progress * 100)}%`);\n            }\n          },\n          tessedit_pageseg_mode: Tesseract.PSM.SINGLE_CHAR,\n          // 单字符模式\n          tessedit_char_whitelist: currentWord.value.text,\n          // 字符白名单\n          preserve_interword_spaces: '0'\n        });\n        let recognizedText = result.data.text.trim();\n\n        // 清理识别结果，只保留中文\n        recognizedText = recognizedText.replace(/[^\\u4e00-\\u9fa5]/g, '');\n\n        // 检查字符\n        const expectedChar = currentWord.value.text[index];\n        const isCorrect = recognizedText === expectedChar;\n        return {\n          recognized: recognizedText,\n          expected: expectedChar,\n          isCorrect: isCorrect\n        };\n      } catch (error) {\n        console.error(`字符${index + 1}识别失败:`, error);\n        throw error;\n      }\n    };\n\n    // 切换到下一个词语\n    const nextWord = () => {\n      currentIndex.value = (currentIndex.value + 1) % words.value.length;\n    };\n\n    // ==================== 生命周期钩子 ====================\n\n    // 组件挂载后的钩子函数\n    onMounted(() => {\n      // 获取画布的2D绘图上下文\n      ctx = canvas.value.getContext('2d');\n\n      // 设置画笔属性\n      ctx.lineWidth = 3; // 线条宽度\n      ctx.lineCap = 'round'; // 线条端点样式为圆形\n      ctx.lineJoin = 'round'; // 线条连接点样式为圆形\n      ctx.strokeStyle = '#000000'; // 线条颜色为黑色\n      ctx.fillStyle = '#000000'; // 填充颜色为黑色\n\n      // 设置高DPI支持，确保在高分辨率屏幕上显示清晰\n      setupHighDPI();\n\n      // 绘制田字格背景\n      drawTianziGrid();\n    });\n\n    // ==================== 监听器 ====================\n\n    // 当前词语改变时重置状态\n    watch(currentWord, () => {\n      resetWordState();\n    });\n    const __returned__ = {\n      words,\n      currentIndex,\n      recognizedText,\n      feedback,\n      isDrawing,\n      isRecognizing,\n      canvas,\n      get ctx() {\n        return ctx;\n      },\n      set ctx(v) {\n        ctx = v;\n      },\n      gridSize,\n      spacing,\n      lastX,\n      lastY,\n      strokeHistory,\n      currentStroke,\n      charResults,\n      recognitionProgress,\n      debugImages,\n      currentDrawingGrid,\n      currentWord,\n      canvasWidth,\n      canvasHeight,\n      allCharsCompleted,\n      setupHighDPI,\n      drawTianziGrid,\n      saveCanvasState,\n      playPronunciation,\n      getEventPos,\n      getGridIndex,\n      startDrawing,\n      draw,\n      stopDrawing,\n      resetWordState,\n      clearCanvas,\n      undoLastStroke,\n      getCharStatusClass,\n      getCharStatusText,\n      showDebugImages,\n      drawExtractionBounds,\n      renderDebugImages,\n      extractCharCanvasAtIndex,\n      recognizeAllChars,\n      recognizeCharAtIndex,\n      nextWord,\n      ref,\n      computed,\n      onMounted,\n      watch,\n      nextTick,\n      get Tesseract() {\n        return Tesseract;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "watch", "nextTick", "Tesseract", "words", "text", "pinyin", "audio", "currentIndex", "recognizedText", "feedback", "isDrawing", "isRecognizing", "canvas", "ctx", "gridSize", "spacing", "lastX", "lastY", "strokeHistory", "currentStroke", "charResults", "recognitionProgress", "debugImages", "currentDrawingGrid", "currentWord", "value", "canvasWidth", "length", "canvasHeight", "allCharsCompleted", "every", "result", "isCorrect", "setupHighDPI", "canvasEl", "dpr", "window", "devicePixelRatio", "width", "height", "style", "scale", "drawTianziGrid", "clearRect", "save", "i", "x", "strokeStyle", "lineWidth", "strokeRect", "beginPath", "moveTo", "lineTo", "stroke", "restore", "lineCap", "lineJoin", "fillStyle", "saveCanvasState", "imageData", "getImageData", "push", "shift", "playPronunciation", "Audio", "play", "catch", "error", "console", "getEventPos", "e", "rect", "getBoundingClientRect", "clientX", "touches", "clientY", "left", "y", "top", "getGridIndex", "gridX", "startDrawing", "pos", "gridIndex", "draw", "stopDrawing", "quadraticCurveTo", "closePath", "resetWordState", "clearCanvas", "undoLastStroke", "lastState", "pop", "putImageData", "getCharStatusClass", "index", "getCharStatusText", "showDebugImages", "drawExtractionBounds", "<PERSON>ar<PERSON><PERSON><PERSON>", "extractCharCanvasAtIndex", "getContext", "renderDebugImages", "setLineDash", "setTimeout", "for<PERSON>ach", "debugImg", "canvasRef", "document", "querySelector", "tempCanvas", "createElement", "tempCtx", "fillRect", "displaySourceX", "displaySourceY", "displayWidth", "displayHeight", "actualSourceX", "actualSourceY", "actualWidth", "actualHeight", "log", "canvasActualWidth", "canvasActualHeight", "canvasDisplayWidth", "canvasDisplayHeight", "drawImage", "recognizeAllChars", "recognizeCharAtIndex", "Promise", "resolve", "recognized", "expected", "correctCount", "filter", "r", "totalCount", "allCorrect", "resultMessage", "map", "join", "details", "message", "recognize", "logger", "m", "status", "Math", "round", "progress", "tessedit_pageseg_mode", "PSM", "SINGLE_CHAR", "tessedit_char_whitelist", "preserve_interword_spaces", "data", "trim", "replace", "expectedChar", "nextWord"], "sources": ["/Volumes/Data/Project/VUE/xx/src/App.vue"], "sourcesContent": ["<template>\n  <!-- Vue应用根容器 -->\n  <div class=\"app-container\">\n    <!-- 应用标题 -->\n    <h1 class=\"title\">中文词语手写测试</h1>\n    \n    <!-- 词语信息显示区域 -->\n    <div class=\"word-info\">\n      <!-- 显示当前词语的拼音 -->\n      <p class=\"pinyin\">拼音: {{ currentWord.pinyin }}</p>\n      <!-- 播放发音按钮 -->\n      <button @click=\"playPronunciation\" class=\"play-btn\">\n        播放发音\n      </button>\n    </div>\n    \n    <!-- 字符状态显示区域 - 显示每个字符的识别状态 -->\n    <div class=\"grid-status\">\n      <div \n        v-for=\"(char, index) in currentWord.text\" \n        :key=\"index\"\n        class=\"char-status\"\n        :class=\"getCharStatusClass(index)\"\n      >\n        {{ char }}\n        <div style=\"font-size: 12px; margin-top: 2px;\">\n          {{ getCharStatusText(index) }}\n        </div>\n      </div>\n    </div>\n\n    <!-- 画布容器 - 包含手写区域 -->\n    <div class=\"canvas-container\" :class=\"{ loading: isRecognizing }\">\n      <!-- 手写画布 - 用户在此区域手写汉字 -->\n      <canvas\n        ref=\"canvas\"\n        :width=\"canvasWidth\"\n        :height=\"canvasHeight\"\n        class=\"writing-canvas\"\n        @mousedown=\"startDrawing\"\n        @mousemove=\"draw\"\n        @mouseup=\"stopDrawing\"\n        @mouseleave=\"stopDrawing\"\n        @touchstart.prevent=\"startDrawing\"\n        @touchmove.prevent=\"draw\"\n        @touchend.prevent=\"stopDrawing\"\n        @touchcancel.prevent=\"stopDrawing\"\n      ></canvas>\n    </div>\n    \n    <!-- 主要操作按钮组 -->\n    <div class=\"button-group\">\n      <!-- 清除画布按钮 -->\n      <button\n        @click=\"clearCanvas\"\n        :disabled=\"isRecognizing\"\n        class=\"btn btn-clear\"\n      >\n        清除画布\n      </button>\n      \n      <!-- 撤销上一笔按钮 -->\n      <button\n        @click=\"undoLastStroke\"\n        :disabled=\"isRecognizing || strokeHistory.length === 0\"\n        class=\"btn btn-undo\"\n      >\n        撤销\n      </button>\n      \n      <!-- 查看提取图像按钮 - 用于调试 -->\n      <button\n        @click=\"showDebugImages\"\n        :disabled=\"isRecognizing\"\n        class=\"btn btn-debug\"\n      >\n        查看提取图像\n      </button>\n      \n      <!-- 提交识别按钮 -->\n      <button\n        @click=\"recognizeAllChars\"\n        :disabled=\"isRecognizing\"\n        class=\"btn btn-submit\"\n      >\n        <div class=\"btn-content\">\n          <span v-if=\"!isRecognizing\">提交识别</span>\n          <span v-else>\n            <div class=\"spinner\"></div>\n            识别中...\n          </span>\n        </div>\n      </button>\n    </div>\n    \n    <!-- 下一个词语按钮 - 当所有字符都识别正确时显示 -->\n    <div class=\"button-group\" v-if=\"allCharsCompleted\">\n      <button\n        @click=\"nextWord\"\n        class=\"btn btn-check\"\n        style=\"width: 100%;\"\n      >\n        下一个词语\n      </button>\n    </div>\n\n    <!-- 识别状态显示区域 -->\n    <div class=\"recognition-status\">\n      <p v-if=\"feedback\" class=\"feedback\" :class=\"feedback.isCorrect ? 'correct' : 'incorrect'\">\n        {{ feedback.message }}\n      </p>\n    </div>\n    \n    <!-- 识别结果显示 -->\n    <p v-if=\"recognizedText && !isRecognizing\" class=\"recognized-text\">\n      识别结果: {{ recognizedText }}\n    </p>\n    \n    <!-- 调试面板 - 显示提取的字符图像 -->\n    <div v-if=\"debugImages.length > 0\" class=\"debug-panel\">\n      <div class=\"debug-title\">提取的字符图像（用于OCR识别）</div>\n      <div class=\"debug-images\">\n        <div v-for=\"(debugImg, index) in debugImages\" :key=\"index\" class=\"debug-char\">\n          <!-- 显示提取的字符图像 -->\n          <canvas\n            :data-ref=\"`debugCanvas${index}`\"\n            :width=\"debugImg.width\"\n            :height=\"debugImg.height\"\n            style=\"width: 80px; height: 80px;\"\n          ></canvas>\n          <!-- 显示字符信息和识别结果 -->\n          <div class=\"debug-char-label\">\n            字符{{ index + 1 }}: {{ currentWord.text[index] }}\n            <br>\n            <span v-if=\"charResults[index]\" :style=\"{ color: charResults[index].isCorrect ? '#28a745' : '#dc3545' }\">\n              识别: {{ charResults[index].recognized || '无' }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\n// 引入Vue 3 Composition API\nimport { ref, computed, onMounted, watch, nextTick } from 'vue'\n\n// 引入Tesseract.js OCR库\nimport Tesseract from 'tesseract.js'\n\n// ==================== 响应式数据定义 ====================\n\n// 词语库 - 包含要练习的中文词语及其拼音和发音\nconst words = ref([\n  { text: '你好', pinyin: 'nǐ hǎo', audio: 'https://dict.youdao.com/dictvoice?audio=nihao&type=1' },\n  { text: '谢谢', pinyin: 'xiè xie', audio: 'https://dict.youdao.com/dictvoice?audio=xiexie&type=1' },\n  { text: '再见', pinyin: 'zài jiàn', audio: 'https://dict.youdao.com/dictvoice?audio=zaijian&type=1' },\n  { text: '朋友', pinyin: 'péng yǒu', audio: 'https://dict.youdao.com/dictvoice?audio=pengyou&type=1' },\n  { text: '家人', pinyin: 'jiā rén', audio: 'https://dict.youdao.com/dictvoice?audio=jiaren&type=1' },\n  { text: '学习', pinyin: 'xué xí', audio: 'https://dict.youdao.com/dictvoice?audio=xuexi&type=1' },\n  { text: '工作', pinyin: 'gōng zuò', audio: 'https://dict.youdao.com/dictvoice?audio=gongzuo&type=1' },\n  { text: '时间', pinyin: 'shí jiān', audio: 'https://dict.youdao.com/dictvoice?audio=shijian&type=1' }\n])\n\n// 基础状态数据\nconst currentIndex = ref(0) // 当前词语的索引\nconst recognizedText = ref('') // OCR识别的文本结果\nconst feedback = ref(null) // 反馈信息对象\nconst isDrawing = ref(false) // 是否正在绘制\nconst isRecognizing = ref(false) // 是否正在进行OCR识别\n\n// Canvas相关数据\nconst canvas = ref(null) // Canvas DOM引用\nlet ctx = null // Canvas 2D绘图上下文\nconst gridSize = ref(120) // 每个田字格的大小（像素）\nconst spacing = ref(15) // 田字格之间的间距（像素）\nconst lastX = ref(0) // 上一个绘制点的X坐标\nconst lastY = ref(0) // 上一个绘制点的Y坐标\n\n// 绘制历史和状态\nconst strokeHistory = ref([]) // 存储每一笔的画布状态，用于撤销功能\nconst currentStroke = ref([]) // 当前正在绘制的笔画点集合\nconst charResults = ref([]) // 每个字符的识别结果数组\nconst recognitionProgress = ref({}) // 识别进度状态对象\nconst debugImages = ref([]) // 调试用的提取图像数组\nconst currentDrawingGrid = ref(-1) // 当前正在绘制的格子索引\n\n// ==================== 计算属性 ====================\n\n// 获取当前词语对象\nconst currentWord = computed(() => {\n  return words.value[currentIndex.value]\n})\n\n// 计算画布宽度 - 根据字符数量、格子大小和间距计算\nconst canvasWidth = computed(() => {\n  return currentWord.value.text.length * gridSize.value + (currentWord.value.text.length - 1) * spacing.value\n})\n\n// 计算画布高度 - 等于单个格子的高度\nconst canvasHeight = computed(() => {\n  return gridSize.value\n})\n\n// 检查是否所有字符都识别正确\nconst allCharsCompleted = computed(() => {\n  return charResults.value.length === currentWord.value.text.length &&\n         charResults.value.every(result => result && result.isCorrect)\n})\n\n// ==================== 方法定义 ====================\n\n// 设置高DPI支持 - 确保在高分辨率屏幕上显示清晰\nconst setupHighDPI = () => {\n  const canvasEl = canvas.value\n  const dpr = window.devicePixelRatio || 1 // 获取设备像素比\n\n  // 设置画布的实际像素大小\n  canvasEl.width = canvasWidth.value * dpr\n  canvasEl.height = canvasHeight.value * dpr\n\n  // 设置画布的CSS显示大小\n  canvasEl.style.width = canvasWidth.value + 'px'\n  canvasEl.style.height = canvasHeight.value + 'px'\n\n  // 缩放绘图上下文以匹配设备像素比\n  ctx.scale(dpr, dpr)\n}\n\n// 绘制田字格背景\nconst drawTianziGrid = () => {\n  // 清除整个画布\n  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)\n\n  // 保存当前绘图状态\n  ctx.save()\n\n  // 为每个字符绘制田字格\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const x = i * (gridSize.value + spacing.value) // 计算当前格子的X坐标\n\n    // 绘制外框 - 使用较粗的线条\n    ctx.strokeStyle = '#999999'\n    ctx.lineWidth = 2\n    ctx.strokeRect(x, 0, gridSize.value, gridSize.value)\n\n    // 绘制内部辅助线 - 使用细线\n    ctx.strokeStyle = '#dddddd'\n    ctx.lineWidth = 1\n\n    // 绘制垂直中线\n    ctx.beginPath()\n    ctx.moveTo(x + gridSize.value / 2, 0)\n    ctx.lineTo(x + gridSize.value / 2, gridSize.value)\n    ctx.stroke()\n\n    // 绘制水平中线\n    ctx.beginPath()\n    ctx.moveTo(x, gridSize.value / 2)\n    ctx.lineTo(x + gridSize.value, gridSize.value / 2)\n    ctx.stroke()\n\n    // 绘制对角线 - 使用更淡的颜色\n    ctx.strokeStyle = '#eeeeee'\n    ctx.lineWidth = 0.5\n\n    // 左上到右下对角线\n    ctx.beginPath()\n    ctx.moveTo(x, 0)\n    ctx.lineTo(x + gridSize.value, gridSize.value)\n    ctx.stroke()\n\n    // 右上到左下对角线\n    ctx.beginPath()\n    ctx.moveTo(x + gridSize.value, 0)\n    ctx.lineTo(x, gridSize.value)\n    ctx.stroke()\n  }\n\n  // 恢复绘图状态，为手写做准备\n  ctx.restore()\n  ctx.lineWidth = 3\n  ctx.lineCap = 'round'\n  ctx.lineJoin = 'round'\n  ctx.strokeStyle = '#000000'\n  ctx.fillStyle = '#000000'\n}\n\n// 保存当前画布状态到历史记录（用于撤销功能）\nconst saveCanvasState = () => {\n  const imageData = ctx.getImageData(0, 0, canvas.value.width, canvas.value.height)\n  strokeHistory.value.push(imageData)\n\n  // 限制历史记录数量，避免内存过多占用\n  if (strokeHistory.value.length > 20) {\n    strokeHistory.value.shift() // 删除最早的记录\n  }\n}\n\n// 播放当前词语的发音\nconst playPronunciation = () => {\n  const audio = new Audio(currentWord.value.audio)\n  audio.play().catch(error => {\n    console.error('播放发音失败:', error)\n  })\n}\n\n// 获取事件在画布中的坐标位置\nconst getEventPos = (e) => {\n  const rect = canvas.value.getBoundingClientRect()\n  const clientX = e.clientX || (e.touches && e.touches[0].clientX)\n  const clientY = e.clientY || (e.touches && e.touches[0].clientY)\n  return {\n    x: (clientX - rect.left) * (canvas.value.width / rect.width) / (window.devicePixelRatio || 1),\n    y: (clientY - rect.top) * (canvas.value.height / rect.height) / (window.devicePixelRatio || 1)\n  }\n}\n\n// 获取坐标所在的格子索引\nconst getGridIndex = (x, y) => {\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const gridX = i * (gridSize.value + spacing.value)\n    if (x >= gridX && x <= gridX + gridSize.value && y >= 0 && y <= gridSize.value) {\n      return i // 返回格子索引\n    }\n  }\n  return -1 // 不在任何格子内\n}\n\n\n\n// 开始绘制 - 鼠标按下或触摸开始时调用\nconst startDrawing = (e) => {\n  if (isRecognizing.value) return // 识别中时禁止绘制\n\n  const pos = getEventPos(e)\n\n  // 检查是否在任何格子内\n  const gridIndex = getGridIndex(pos.x, pos.y)\n  if (gridIndex === -1) return // 不在格子内则不开始绘制\n\n  // 记录当前绘制的格子索引\n  currentDrawingGrid.value = gridIndex\n\n  // 在开始新的笔画前保存当前状态\n  saveCanvasState()\n\n  isDrawing.value = true\n  lastX.value = pos.x\n  lastY.value = pos.y\n  currentStroke.value = [pos]\n  ctx.beginPath()\n  ctx.moveTo(pos.x, pos.y)\n}\n\n// 绘制过程 - 鼠标移动或触摸移动时调用\nconst draw = (e) => {\n  if (!isDrawing.value || isRecognizing.value) return\n  const pos = getEventPos(e)\n\n  // 检查是否还在同一个格子内\n  const gridIndex = getGridIndex(pos.x, pos.y)\n  if (gridIndex !== currentDrawingGrid.value) {\n    stopDrawing() // 离开当前格子则停止绘制\n    return\n  }\n\n  // 记录当前笔画的点\n  currentStroke.value.push(pos)\n\n  // 使用二次贝塞尔曲线使线条更平滑\n  ctx.quadraticCurveTo(lastX.value, lastY.value, (pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2)\n  ctx.stroke()\n  ctx.beginPath()\n  ctx.moveTo((pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2)\n\n  lastX.value = pos.x\n  lastY.value = pos.y\n}\n\n// 停止绘制 - 鼠标抬起或触摸结束时调用\nconst stopDrawing = () => {\n  if (isDrawing.value) {\n    isDrawing.value = false\n    ctx.closePath()\n    currentStroke.value = [] // 清空当前笔画\n    currentDrawingGrid.value = -1 // 重置当前绘制格子\n  }\n}\n\n// 重置词语状态 - 切换到新词语时调用\nconst resetWordState = () => {\n  charResults.value = []\n  recognitionProgress.value = {}\n  debugImages.value = []\n  clearCanvas()\n}\n\n// 清除画布\nconst clearCanvas = () => {\n  drawTianziGrid() // 清除后重新绘制田字格\n  strokeHistory.value = [] // 清空撤销历史\n  currentStroke.value = []\n  recognizedText.value = ''\n  feedback.value = null\n}\n\n// 撤销上一笔绘制\nconst undoLastStroke = () => {\n  if (strokeHistory.value.length === 0) return\n\n  // 恢复到上一个状态\n  const lastState = strokeHistory.value.pop()\n  ctx.putImageData(lastState, 0, 0)\n\n  recognizedText.value = ''\n  feedback.value = null\n}\n\n// 获取字符状态的CSS类名\nconst getCharStatusClass = (index) => {\n  if (recognitionProgress.value[index] === 'recognizing') return 'current'\n  if (charResults.value[index]) {\n    return charResults.value[index].isCorrect ? 'correct' : 'incorrect'\n  }\n  return 'pending'\n}\n\n// 获取字符状态的显示文本\nconst getCharStatusText = (index) => {\n  if (recognitionProgress.value[index] === 'recognizing') return '识别中'\n  if (charResults.value[index]) {\n    return charResults.value[index].isCorrect ? '正确' : '错误'\n  }\n  return '待识别'\n}\n\n// 显示调试图像 - 提取每个字符的图像用于调试\nconst showDebugImages = () => {\n  debugImages.value = []\n\n  // 先在原canvas上绘制提取区域边界（用于调试）\n  drawExtractionBounds()\n\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const charCanvas = extractCharCanvasAtIndex(i)\n\n    // 保存图像数据\n    debugImages.value.push({\n      canvas: charCanvas,\n      width: charCanvas.width,\n      height: charCanvas.height,\n      imageData: charCanvas.getContext('2d').getImageData(0, 0, charCanvas.width, charCanvas.height)\n    })\n  }\n\n  // 等待Vue更新DOM后绘制调试图像\n  nextTick(() => {\n    renderDebugImages()\n  })\n}\n\n// 绘制提取区域边界 - 用于调试\nconst drawExtractionBounds = () => {\n  ctx.save()\n  ctx.strokeStyle = 'red'\n  ctx.lineWidth = 2\n  ctx.setLineDash([5, 5]) // 虚线样式\n\n  for (let i = 0; i < currentWord.value.text.length; i++) {\n    const x = i * (gridSize.value + spacing.value)\n    ctx.strokeRect(x, 0, gridSize.value, gridSize.value)\n  }\n\n  ctx.restore()\n\n  // 2秒后清除边界线\n  setTimeout(() => {\n    drawTianziGrid()\n  }, 2000)\n}\n\n// 渲染调试图像到调试面板\nconst renderDebugImages = () => {\n  debugImages.value.forEach((debugImg, index) => {\n    const canvasRef = document.querySelector(`[data-ref=\"debugCanvas${index}\"]`)\n    if (canvasRef) {\n      const ctx = canvasRef.getContext('2d')\n\n      // 清除画布\n      ctx.clearRect(0, 0, canvasRef.width, canvasRef.height)\n\n      // 绘制提取的图像\n      ctx.putImageData(debugImg.imageData, 0, 0)\n    }\n  })\n}\n\n// 提取指定索引位置的字符画布\nconst extractCharCanvasAtIndex = (index) => {\n  // 创建临时canvas，只包含指定字符格子的内容\n  const tempCanvas = document.createElement('canvas')\n  const tempCtx = tempCanvas.getContext('2d')\n\n  // 获取设备像素比\n  const dpr = window.devicePixelRatio || 1\n\n  // 设置临时canvas大小（考虑DPI）\n  tempCanvas.width = gridSize.value * dpr\n  tempCanvas.height = gridSize.value * dpr\n\n  // 设置CSS显示大小\n  tempCanvas.style.width = gridSize.value + 'px'\n  tempCanvas.style.height = gridSize.value + 'px'\n\n  // 缩放绘图上下文\n  tempCtx.scale(dpr, dpr)\n\n  // 填充白色背景\n  tempCtx.fillStyle = 'white'\n  tempCtx.fillRect(0, 0, gridSize.value, gridSize.value)\n\n  // 计算指定格子在显示坐标系中的位置（不考虑DPI）\n  const displaySourceX = index * (gridSize.value + spacing.value)\n  const displaySourceY = 0\n  const displayWidth = gridSize.value\n  const displayHeight = gridSize.value\n\n  // 计算在实际canvas像素中的位置（考虑DPI）\n  const actualSourceX = displaySourceX * dpr\n  const actualSourceY = displaySourceY * dpr\n  const actualWidth = displayWidth * dpr\n  const actualHeight = displayHeight * dpr\n\n  console.log(`提取字符${index + 1}:`, {\n    index: index,\n    displaySourceX: displaySourceX,\n    displaySourceY: displaySourceY,\n    displayWidth: displayWidth,\n    displayHeight: displayHeight,\n    actualSourceX: actualSourceX,\n    actualSourceY: actualSourceY,\n    actualWidth: actualWidth,\n    actualHeight: actualHeight,\n    dpr: dpr,\n    canvasActualWidth: canvas.value.width,\n    canvasActualHeight: canvas.value.height,\n    canvasDisplayWidth: canvas.value.style.width,\n    canvasDisplayHeight: canvas.value.style.height\n  })\n\n  // 从原canvas中提取指定格子的内容\n  // 注意：这里使用显示坐标系，因为tempCtx已经缩放了\n  tempCtx.drawImage(\n    canvas.value,\n    actualSourceX, actualSourceY, actualWidth, actualHeight,  // 源区域（实际像素）\n    0, 0, displayWidth, displayHeight                         // 目标区域（显示坐标）\n  )\n\n  return tempCanvas\n}\n\n// 识别所有字符 - 主要的OCR识别函数\nconst recognizeAllChars = async () => {\n  if (isRecognizing.value) return\n\n  isRecognizing.value = true\n  recognizedText.value = ''\n  feedback.value = null\n  charResults.value = []\n  recognitionProgress.value = {}\n\n  // 先显示调试图像\n  showDebugImages()\n\n  try {\n    // 顺序识别每个字符（一个一个提交OCR）\n    for (let i = 0; i < currentWord.value.text.length; i++) {\n      recognitionProgress.value[i] = 'recognizing'\n\n      try {\n        console.log(`开始识别第${i + 1}个字符: ${currentWord.value.text[i]}`)\n        const result = await recognizeCharAtIndex(i)\n        charResults.value[i] = result\n        recognitionProgress.value[i] = 'completed'\n        console.log(`第${i + 1}个字符识别完成:`, result)\n\n        // 短暂延迟，让用户看到进度\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n      } catch (error) {\n        console.error(`第${i + 1}个字符识别失败:`, error)\n        charResults.value[i] = {\n          recognized: '',\n          expected: currentWord.value.text[i],\n          isCorrect: false\n        }\n        recognitionProgress.value[i] = 'completed'\n      }\n    }\n\n    // 处理最终结果\n    const correctCount = charResults.value.filter(r => r.isCorrect).length\n    const totalCount = charResults.value.length\n    const allCorrect = correctCount === totalCount\n\n    let resultMessage = ''\n    if (allCorrect) {\n      resultMessage = `全部正确！识别结果：${charResults.value.map(r => r.recognized).join('')}`\n    } else {\n      resultMessage = `识别完成：${correctCount}/${totalCount} 正确`\n      // 显示详细结果\n      const details = charResults.value.map((r, i) =>\n        `${currentWord.value.text[i]}→${r.recognized}${r.isCorrect ? '✓' : '✗'}`\n      ).join(' ')\n      resultMessage += `\\n详情：${details}`\n    }\n\n    feedback.value = {\n      isCorrect: allCorrect,\n      message: resultMessage\n    }\n\n    if (allCorrect) {\n      setTimeout(() => {\n        feedback.value = {\n          isCorrect: true,\n          message: '恭喜！可以进入下一个词语了'\n        }\n      }, 2000)\n    }\n\n  } catch (error) {\n    console.error('识别失败:', error)\n    feedback.value = {\n      isCorrect: false,\n      message: '识别失败，请重试'\n    }\n  } finally {\n    isRecognizing.value = false\n  }\n}\n\n// 识别指定索引位置的字符\nconst recognizeCharAtIndex = async (index) => {\n  try {\n    // 创建只包含指定格子的canvas\n    const charCanvas = extractCharCanvasAtIndex(index)\n\n    const result = await Tesseract.recognize(\n      charCanvas,\n      'chi_sim', // 简体中文\n      {\n        logger: (m) => {\n          if (m.status === 'recognizing text') {\n            console.log(`字符${index + 1}识别进度: ${Math.round(m.progress * 100)}%`)\n          }\n        },\n        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_CHAR, // 单字符模式\n        tessedit_char_whitelist: currentWord.value.text, // 字符白名单\n        preserve_interword_spaces: '0'\n      }\n    )\n\n    let recognizedText = result.data.text.trim()\n\n    // 清理识别结果，只保留中文\n    recognizedText = recognizedText.replace(/[^\\u4e00-\\u9fa5]/g, '')\n\n    // 检查字符\n    const expectedChar = currentWord.value.text[index]\n    const isCorrect = recognizedText === expectedChar\n\n    return {\n      recognized: recognizedText,\n      expected: expectedChar,\n      isCorrect: isCorrect\n    }\n\n  } catch (error) {\n    console.error(`字符${index + 1}识别失败:`, error)\n    throw error\n  }\n}\n\n// 切换到下一个词语\nconst nextWord = () => {\n  currentIndex.value = (currentIndex.value + 1) % words.value.length\n}\n\n// ==================== 生命周期钩子 ====================\n\n// 组件挂载后的钩子函数\nonMounted(() => {\n  // 获取画布的2D绘图上下文\n  ctx = canvas.value.getContext('2d')\n\n  // 设置画笔属性\n  ctx.lineWidth = 3 // 线条宽度\n  ctx.lineCap = 'round' // 线条端点样式为圆形\n  ctx.lineJoin = 'round' // 线条连接点样式为圆形\n  ctx.strokeStyle = '#000000' // 线条颜色为黑色\n  ctx.fillStyle = '#000000' // 填充颜色为黑色\n\n  // 设置高DPI支持，确保在高分辨率屏幕上显示清晰\n  setupHighDPI()\n\n  // 绘制田字格背景\n  drawTianziGrid()\n})\n\n// ==================== 监听器 ====================\n\n// 当前词语改变时重置状态\nwatch(currentWord, () => {\n  resetWordState()\n})\n</script>\n\n<style>\n/* 全局样式重置 - 清除默认边距和内边距 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box; /* 使用边框盒模型 */\n}\n\n/* 页面主体样式 */\nbody {\n  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif; /* 中文友好字体 */\n  background-color: #f5f5f5; /* 浅灰色背景 */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh; /* 最小高度为视窗高度 */\n  padding: 20px;\n}\n\n/* 应用主容器样式 */\n.app-container {\n  background: white;\n  padding: 30px;\n  border-radius: 12px; /* 圆角边框 */\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* 阴影效果 */\n  width: 100%;\n  max-width: 600px; /* 最大宽度限制 */\n}\n\n/* 标题样式 */\n.title {\n  font-size: 28px;\n  font-weight: bold;\n  text-align: center;\n  margin-bottom: 30px;\n  color: #333;\n}\n\n/* 词语信息区域样式 */\n.word-info {\n  margin-bottom: 25px;\n  text-align: center;\n}\n\n/* 拼音显示样式 */\n.pinyin {\n  font-size: 20px;\n  color: #666;\n  margin-bottom: 15px;\n}\n\n/* 播放发音按钮样式 */\n.play-btn {\n  background: #007bff; /* 蓝色背景 */\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 16px;\n  transition: background-color 0.3s; /* 背景色过渡动画 */\n}\n\n/* 播放按钮悬停效果 */\n.play-btn:hover {\n  background: #0056b3; /* 深蓝色 */\n}\n\n/* 画布容器样式 */\n.canvas-container {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  margin: 25px 0;\n  background: #fafafa; /* 浅灰色背景 */\n  border-radius: 8px;\n  padding: 20px;\n}\n\n/* 手写画布样式 */\n.writing-canvas {\n  background: white;\n  cursor: crosshair; /* 十字光标 */\n  border-radius: 4px;\n}\n\n/* 加载状态样式 - 识别时的半透明效果 */\n.loading {\n  opacity: 0.6;\n  pointer-events: none; /* 禁用鼠标事件 */\n}\n\n/* 按钮组容器样式 */\n.button-group {\n  display: flex;\n  justify-content: space-between;\n  gap: 15px; /* 按钮间距 */\n  margin-bottom: 25px;\n}\n\n/* 通用按钮样式 */\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 16px;\n  transition: all 0.3s; /* 所有属性过渡动画 */\n  flex: 1; /* 等宽分布 */\n}\n\n/* 禁用状态按钮样式 */\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 清除按钮样式 */\n.btn-clear {\n  background: #6c757d; /* 灰色 */\n  color: white;\n}\n\n.btn-clear:hover:not(:disabled) {\n  background: #545b62; /* 深灰色 */\n}\n\n/* 提交按钮样式 */\n.btn-submit {\n  background: #28a745; /* 绿色 */\n  color: white;\n}\n\n.btn-submit:hover:not(:disabled) {\n  background: #1e7e34; /* 深绿色 */\n}\n\n/* 撤销按钮样式 */\n.btn-undo {\n  background: #ffc107; /* 黄色 */\n  color: #212529; /* 深色文字 */\n}\n\n.btn-undo:hover:not(:disabled) {\n  background: #e0a800; /* 深黄色 */\n}\n\n/* 检查按钮样式 */\n.btn-check {\n  background: #17a2b8; /* 青色 */\n  color: white;\n}\n\n.btn-check:hover:not(:disabled) {\n  background: #138496; /* 深青色 */\n}\n\n/* 字符状态显示区域样式 */\n.grid-status {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin: 15px 0;\n  flex-wrap: wrap; /* 允许换行 */\n}\n\n/* 单个字符状态样式 */\n.char-status {\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 14px;\n  font-weight: bold;\n  min-width: 60px;\n  text-align: center;\n}\n\n/* 待识别状态样式 */\n.char-status.pending {\n  background: #f8f9fa;\n  color: #6c757d;\n  border: 2px solid #dee2e6;\n}\n\n/* 识别正确状态样式 */\n.char-status.correct {\n  background: #d4edda;\n  color: #155724;\n  border: 2px solid #c3e6cb;\n}\n\n/* 识别错误状态样式 */\n.char-status.incorrect {\n  background: #f8d7da;\n  color: #721c24;\n  border: 2px solid #f5c6cb;\n}\n\n/* 当前识别中状态样式 */\n.char-status.current {\n  background: #fff3cd;\n  color: #856404;\n  border: 2px solid #ffeaa7;\n}\n\n/* 调试面板样式 */\n.debug-panel {\n  margin: 20px 0;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #dee2e6;\n}\n\n/* 调试标题样式 */\n.debug-title {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #495057;\n}\n\n/* 调试图像容器样式 */\n.debug-images {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n/* 单个调试字符样式 */\n.debug-char {\n  text-align: center;\n}\n\n/* 调试画布样式 */\n.debug-char canvas {\n  border: 1px solid #ccc;\n  background: white;\n}\n\n/* 调试字符标签样式 */\n.debug-char-label {\n  font-size: 12px;\n  margin-top: 5px;\n  color: #666;\n}\n\n/* 调试按钮样式 */\n.btn-debug {\n  background: #6f42c1; /* 紫色 */\n  color: white;\n}\n\n.btn-debug:hover:not(:disabled) {\n  background: #5a32a3; /* 深紫色 */\n}\n\n/* 按钮内容容器样式 */\n.btn-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 加载动画样式 */\n.spinner {\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #ffffff;\n  border-radius: 50%;\n  width: 18px;\n  height: 18px;\n  animation: spin 1s linear infinite; /* 旋转动画 */\n  margin-right: 8px;\n}\n\n/* 旋转动画关键帧 */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 识别状态显示区域样式 */\n.recognition-status {\n  min-height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15px;\n}\n\n/* 反馈信息样式 */\n.feedback {\n  text-align: center;\n  font-size: 18px;\n  font-weight: bold;\n}\n\n/* 正确反馈样式 */\n.feedback.correct {\n  color: #28a745;\n}\n\n/* 错误反馈样式 */\n.feedback.incorrect {\n  color: #dc3545;\n}\n\n/* 识别结果文本样式 */\n.recognized-text {\n  text-align: center;\n  color: #666;\n  font-size: 16px;\n  margin-top: 10px;\n}\n</style>\n"], "mappings": ";;;;;;AAkJA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,KAAI;;AAE9D;AACA,OAAOC,SAAS,MAAM,cAAa;;AAEnC;;AAEA;;;;;;;;;IARA;IASA,MAAMC,KAAK,GAAGN,GAAG,CAAC,CAChB;MAAEO,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAuD,CAAC,EAC/F;MAAEF,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAwD,CAAC,EACjG;MAAEF,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAyD,CAAC,EACnG;MAAEF,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAyD,CAAC,EACnG;MAAEF,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAwD,CAAC,EACjG;MAAEF,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAuD,CAAC,EAC/F;MAAEF,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAyD,CAAC,EACnG;MAAEF,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAyD,EACnG;;IAED;IACA,MAAMC,YAAY,GAAGV,GAAG,CAAC,CAAC,CAAC,EAAC;IAC5B,MAAMW,cAAc,GAAGX,GAAG,CAAC,EAAE,CAAC,EAAC;IAC/B,MAAMY,QAAQ,GAAGZ,GAAG,CAAC,IAAI,CAAC,EAAC;IAC3B,MAAMa,SAAS,GAAGb,GAAG,CAAC,KAAK,CAAC,EAAC;IAC7B,MAAMc,aAAa,GAAGd,GAAG,CAAC,KAAK,CAAC,EAAC;;IAEjC;IACA,MAAMe,MAAM,GAAGf,GAAG,CAAC,IAAI,CAAC,EAAC;IACzB,IAAIgB,GAAG,GAAG,IAAI,EAAC;IACf,MAAMC,QAAQ,GAAGjB,GAAG,CAAC,GAAG,CAAC,EAAC;IAC1B,MAAMkB,OAAO,GAAGlB,GAAG,CAAC,EAAE,CAAC,EAAC;IACxB,MAAMmB,KAAK,GAAGnB,GAAG,CAAC,CAAC,CAAC,EAAC;IACrB,MAAMoB,KAAK,GAAGpB,GAAG,CAAC,CAAC,CAAC,EAAC;;IAErB;IACA,MAAMqB,aAAa,GAAGrB,GAAG,CAAC,EAAE,CAAC,EAAC;IAC9B,MAAMsB,aAAa,GAAGtB,GAAG,CAAC,EAAE,CAAC,EAAC;IAC9B,MAAMuB,WAAW,GAAGvB,GAAG,CAAC,EAAE,CAAC,EAAC;IAC5B,MAAMwB,mBAAmB,GAAGxB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC;IACpC,MAAMyB,WAAW,GAAGzB,GAAG,CAAC,EAAE,CAAC,EAAC;IAC5B,MAAM0B,kBAAkB,GAAG1B,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC;;IAEnC;;IAEA;IACA,MAAM2B,WAAW,GAAG1B,QAAQ,CAAC,MAAM;MACjC,OAAOK,KAAK,CAACsB,KAAK,CAAClB,YAAY,CAACkB,KAAK;IACvC,CAAC;;IAED;IACA,MAAMC,WAAW,GAAG5B,QAAQ,CAAC,MAAM;MACjC,OAAO0B,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,GAAGb,QAAQ,CAACW,KAAK,GAAG,CAACD,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,GAAG,CAAC,IAAIZ,OAAO,CAACU,KAAI;IAC5G,CAAC;;IAED;IACA,MAAMG,YAAY,GAAG9B,QAAQ,CAAC,MAAM;MAClC,OAAOgB,QAAQ,CAACW,KAAI;IACtB,CAAC;;IAED;IACA,MAAMI,iBAAiB,GAAG/B,QAAQ,CAAC,MAAM;MACvC,OAAOsB,WAAW,CAACK,KAAK,CAACE,MAAM,KAAKH,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,IAC1DP,WAAW,CAACK,KAAK,CAACK,KAAK,CAACC,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACC,SAAS;IACrE,CAAC;;IAED;;IAEA;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAGtB,MAAM,CAACa,KAAI;MAC5B,MAAMU,GAAG,GAAGC,MAAM,CAACC,gBAAgB,IAAI,CAAC,EAAC;;MAEzC;MACAH,QAAQ,CAACI,KAAK,GAAGZ,WAAW,CAACD,KAAK,GAAGU,GAAE;MACvCD,QAAQ,CAACK,MAAM,GAAGX,YAAY,CAACH,KAAK,GAAGU,GAAE;;MAEzC;MACAD,QAAQ,CAACM,KAAK,CAACF,KAAK,GAAGZ,WAAW,CAACD,KAAK,GAAG,IAAG;MAC9CS,QAAQ,CAACM,KAAK,CAACD,MAAM,GAAGX,YAAY,CAACH,KAAK,GAAG,IAAG;;MAEhD;MACAZ,GAAG,CAAC4B,KAAK,CAACN,GAAG,EAAEA,GAAG;IACpB;;IAEA;IACA,MAAMO,cAAc,GAAGA,CAAA,KAAM;MAC3B;MACA7B,GAAG,CAAC8B,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEjB,WAAW,CAACD,KAAK,EAAEG,YAAY,CAACH,KAAK;;MAEzD;MACAZ,GAAG,CAAC+B,IAAI,CAAC;;MAET;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,EAAEkB,CAAC,EAAE,EAAE;QACtD,MAAMC,CAAC,GAAGD,CAAC,IAAI/B,QAAQ,CAACW,KAAK,GAAGV,OAAO,CAACU,KAAK,CAAC,EAAC;;QAE/C;QACAZ,GAAG,CAACkC,WAAW,GAAG,SAAQ;QAC1BlC,GAAG,CAACmC,SAAS,GAAG;QAChBnC,GAAG,CAACoC,UAAU,CAACH,CAAC,EAAE,CAAC,EAAEhC,QAAQ,CAACW,KAAK,EAAEX,QAAQ,CAACW,KAAK;;QAEnD;QACAZ,GAAG,CAACkC,WAAW,GAAG,SAAQ;QAC1BlC,GAAG,CAACmC,SAAS,GAAG;;QAEhB;QACAnC,GAAG,CAACqC,SAAS,CAAC;QACdrC,GAAG,CAACsC,MAAM,CAACL,CAAC,GAAGhC,QAAQ,CAACW,KAAK,GAAG,CAAC,EAAE,CAAC;QACpCZ,GAAG,CAACuC,MAAM,CAACN,CAAC,GAAGhC,QAAQ,CAACW,KAAK,GAAG,CAAC,EAAEX,QAAQ,CAACW,KAAK;QACjDZ,GAAG,CAACwC,MAAM,CAAC;;QAEX;QACAxC,GAAG,CAACqC,SAAS,CAAC;QACdrC,GAAG,CAACsC,MAAM,CAACL,CAAC,EAAEhC,QAAQ,CAACW,KAAK,GAAG,CAAC;QAChCZ,GAAG,CAACuC,MAAM,CAACN,CAAC,GAAGhC,QAAQ,CAACW,KAAK,EAAEX,QAAQ,CAACW,KAAK,GAAG,CAAC;QACjDZ,GAAG,CAACwC,MAAM,CAAC;;QAEX;QACAxC,GAAG,CAACkC,WAAW,GAAG,SAAQ;QAC1BlC,GAAG,CAACmC,SAAS,GAAG,GAAE;;QAElB;QACAnC,GAAG,CAACqC,SAAS,CAAC;QACdrC,GAAG,CAACsC,MAAM,CAACL,CAAC,EAAE,CAAC;QACfjC,GAAG,CAACuC,MAAM,CAACN,CAAC,GAAGhC,QAAQ,CAACW,KAAK,EAAEX,QAAQ,CAACW,KAAK;QAC7CZ,GAAG,CAACwC,MAAM,CAAC;;QAEX;QACAxC,GAAG,CAACqC,SAAS,CAAC;QACdrC,GAAG,CAACsC,MAAM,CAACL,CAAC,GAAGhC,QAAQ,CAACW,KAAK,EAAE,CAAC;QAChCZ,GAAG,CAACuC,MAAM,CAACN,CAAC,EAAEhC,QAAQ,CAACW,KAAK;QAC5BZ,GAAG,CAACwC,MAAM,CAAC;MACb;;MAEA;MACAxC,GAAG,CAACyC,OAAO,CAAC;MACZzC,GAAG,CAACmC,SAAS,GAAG;MAChBnC,GAAG,CAAC0C,OAAO,GAAG,OAAM;MACpB1C,GAAG,CAAC2C,QAAQ,GAAG,OAAM;MACrB3C,GAAG,CAACkC,WAAW,GAAG,SAAQ;MAC1BlC,GAAG,CAAC4C,SAAS,GAAG,SAAQ;IAC1B;;IAEA;IACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,SAAS,GAAG9C,GAAG,CAAC+C,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEhD,MAAM,CAACa,KAAK,CAACa,KAAK,EAAE1B,MAAM,CAACa,KAAK,CAACc,MAAM;MAChFrB,aAAa,CAACO,KAAK,CAACoC,IAAI,CAACF,SAAS;;MAElC;MACA,IAAIzC,aAAa,CAACO,KAAK,CAACE,MAAM,GAAG,EAAE,EAAE;QACnCT,aAAa,CAACO,KAAK,CAACqC,KAAK,CAAC,CAAC,EAAC;MAC9B;IACF;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAMzD,KAAK,GAAG,IAAI0D,KAAK,CAACxC,WAAW,CAACC,KAAK,CAACnB,KAAK;MAC/CA,KAAK,CAAC2D,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;QAC1BC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;MAChC,CAAC;IACH;;IAEA;IACA,MAAME,WAAW,GAAIC,CAAC,IAAK;MACzB,MAAMC,IAAI,GAAG3D,MAAM,CAACa,KAAK,CAAC+C,qBAAqB,CAAC;MAChD,MAAMC,OAAO,GAAGH,CAAC,CAACG,OAAO,IAAKH,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACD,OAAO;MAC/D,MAAME,OAAO,GAAGL,CAAC,CAACK,OAAO,IAAKL,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;MAC/D,OAAO;QACL7B,CAAC,EAAE,CAAC2B,OAAO,GAAGF,IAAI,CAACK,IAAI,KAAKhE,MAAM,CAACa,KAAK,CAACa,KAAK,GAAGiC,IAAI,CAACjC,KAAK,CAAC,IAAIF,MAAM,CAACC,gBAAgB,IAAI,CAAC,CAAC;QAC7FwC,CAAC,EAAE,CAACF,OAAO,GAAGJ,IAAI,CAACO,GAAG,KAAKlE,MAAM,CAACa,KAAK,CAACc,MAAM,GAAGgC,IAAI,CAAChC,MAAM,CAAC,IAAIH,MAAM,CAACC,gBAAgB,IAAI,CAAC;MAC/F;IACF;;IAEA;IACA,MAAM0C,YAAY,GAAGA,CAACjC,CAAC,EAAE+B,CAAC,KAAK;MAC7B,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,EAAEkB,CAAC,EAAE,EAAE;QACtD,MAAMmC,KAAK,GAAGnC,CAAC,IAAI/B,QAAQ,CAACW,KAAK,GAAGV,OAAO,CAACU,KAAK;QACjD,IAAIqB,CAAC,IAAIkC,KAAK,IAAIlC,CAAC,IAAIkC,KAAK,GAAGlE,QAAQ,CAACW,KAAK,IAAIoD,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI/D,QAAQ,CAACW,KAAK,EAAE;UAC9E,OAAOoB,CAAC,EAAC;QACX;MACF;MACA,OAAO,CAAC,CAAC,EAAC;IACZ;;IAIA;IACA,MAAMoC,YAAY,GAAIX,CAAC,IAAK;MAC1B,IAAI3D,aAAa,CAACc,KAAK,EAAE,OAAM,CAAC;;MAEhC,MAAMyD,GAAG,GAAGb,WAAW,CAACC,CAAC;;MAEzB;MACA,MAAMa,SAAS,GAAGJ,YAAY,CAACG,GAAG,CAACpC,CAAC,EAAEoC,GAAG,CAACL,CAAC;MAC3C,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE,OAAM,CAAC;;MAE7B;MACA5D,kBAAkB,CAACE,KAAK,GAAG0D,SAAQ;;MAEnC;MACAzB,eAAe,CAAC;MAEhBhD,SAAS,CAACe,KAAK,GAAG,IAAG;MACrBT,KAAK,CAACS,KAAK,GAAGyD,GAAG,CAACpC,CAAA;MAClB7B,KAAK,CAACQ,KAAK,GAAGyD,GAAG,CAACL,CAAA;MAClB1D,aAAa,CAACM,KAAK,GAAG,CAACyD,GAAG;MAC1BrE,GAAG,CAACqC,SAAS,CAAC;MACdrC,GAAG,CAACsC,MAAM,CAAC+B,GAAG,CAACpC,CAAC,EAAEoC,GAAG,CAACL,CAAC;IACzB;;IAEA;IACA,MAAMO,IAAI,GAAId,CAAC,IAAK;MAClB,IAAI,CAAC5D,SAAS,CAACe,KAAK,IAAId,aAAa,CAACc,KAAK,EAAE;MAC7C,MAAMyD,GAAG,GAAGb,WAAW,CAACC,CAAC;;MAEzB;MACA,MAAMa,SAAS,GAAGJ,YAAY,CAACG,GAAG,CAACpC,CAAC,EAAEoC,GAAG,CAACL,CAAC;MAC3C,IAAIM,SAAS,KAAK5D,kBAAkB,CAACE,KAAK,EAAE;QAC1C4D,WAAW,CAAC,CAAC,EAAC;QACd;MACF;;MAEA;MACAlE,aAAa,CAACM,KAAK,CAACoC,IAAI,CAACqB,GAAG;;MAE5B;MACArE,GAAG,CAACyE,gBAAgB,CAACtE,KAAK,CAACS,KAAK,EAAER,KAAK,CAACQ,KAAK,EAAE,CAACyD,GAAG,CAACpC,CAAC,GAAG9B,KAAK,CAACS,KAAK,IAAI,CAAC,EAAE,CAACyD,GAAG,CAACL,CAAC,GAAG5D,KAAK,CAACQ,KAAK,IAAI,CAAC;MACnGZ,GAAG,CAACwC,MAAM,CAAC;MACXxC,GAAG,CAACqC,SAAS,CAAC;MACdrC,GAAG,CAACsC,MAAM,CAAC,CAAC+B,GAAG,CAACpC,CAAC,GAAG9B,KAAK,CAACS,KAAK,IAAI,CAAC,EAAE,CAACyD,GAAG,CAACL,CAAC,GAAG5D,KAAK,CAACQ,KAAK,IAAI,CAAC;MAE/DT,KAAK,CAACS,KAAK,GAAGyD,GAAG,CAACpC,CAAA;MAClB7B,KAAK,CAACQ,KAAK,GAAGyD,GAAG,CAACL,CAAA;IACpB;;IAEA;IACA,MAAMQ,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAI3E,SAAS,CAACe,KAAK,EAAE;QACnBf,SAAS,CAACe,KAAK,GAAG,KAAI;QACtBZ,GAAG,CAAC0E,SAAS,CAAC;QACdpE,aAAa,CAACM,KAAK,GAAG,EAAE,EAAC;QACzBF,kBAAkB,CAACE,KAAK,GAAG,CAAC,CAAC,EAAC;MAChC;IACF;;IAEA;IACA,MAAM+D,cAAc,GAAGA,CAAA,KAAM;MAC3BpE,WAAW,CAACK,KAAK,GAAG,EAAC;MACrBJ,mBAAmB,CAACI,KAAK,GAAG,CAAC;MAC7BH,WAAW,CAACG,KAAK,GAAG,EAAC;MACrBgE,WAAW,CAAC;IACd;;IAEA;IACA,MAAMA,WAAW,GAAGA,CAAA,KAAM;MACxB/C,cAAc,CAAC,CAAC,EAAC;MACjBxB,aAAa,CAACO,KAAK,GAAG,EAAE,EAAC;MACzBN,aAAa,CAACM,KAAK,GAAG,EAAC;MACvBjB,cAAc,CAACiB,KAAK,GAAG,EAAC;MACxBhB,QAAQ,CAACgB,KAAK,GAAG,IAAG;IACtB;;IAEA;IACA,MAAMiE,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIxE,aAAa,CAACO,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;;MAEtC;MACA,MAAMgE,SAAS,GAAGzE,aAAa,CAACO,KAAK,CAACmE,GAAG,CAAC;MAC1C/E,GAAG,CAACgF,YAAY,CAACF,SAAS,EAAE,CAAC,EAAE,CAAC;MAEhCnF,cAAc,CAACiB,KAAK,GAAG,EAAC;MACxBhB,QAAQ,CAACgB,KAAK,GAAG,IAAG;IACtB;;IAEA;IACA,MAAMqE,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI1E,mBAAmB,CAACI,KAAK,CAACsE,KAAK,CAAC,KAAK,aAAa,EAAE,OAAO,SAAQ;MACvE,IAAI3E,WAAW,CAACK,KAAK,CAACsE,KAAK,CAAC,EAAE;QAC5B,OAAO3E,WAAW,CAACK,KAAK,CAACsE,KAAK,CAAC,CAAC/D,SAAS,GAAG,SAAS,GAAG,WAAU;MACpE;MACA,OAAO,SAAQ;IACjB;;IAEA;IACA,MAAMgE,iBAAiB,GAAID,KAAK,IAAK;MACnC,IAAI1E,mBAAmB,CAACI,KAAK,CAACsE,KAAK,CAAC,KAAK,aAAa,EAAE,OAAO,KAAI;MACnE,IAAI3E,WAAW,CAACK,KAAK,CAACsE,KAAK,CAAC,EAAE;QAC5B,OAAO3E,WAAW,CAACK,KAAK,CAACsE,KAAK,CAAC,CAAC/D,SAAS,GAAG,IAAI,GAAG,IAAG;MACxD;MACA,OAAO,KAAI;IACb;;IAEA;IACA,MAAMiE,eAAe,GAAGA,CAAA,KAAM;MAC5B3E,WAAW,CAACG,KAAK,GAAG,EAAC;;MAErB;MACAyE,oBAAoB,CAAC;MAErB,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,EAAEkB,CAAC,EAAE,EAAE;QACtD,MAAMsD,UAAU,GAAGC,wBAAwB,CAACvD,CAAC;;QAE7C;QACAvB,WAAW,CAACG,KAAK,CAACoC,IAAI,CAAC;UACrBjD,MAAM,EAAEuF,UAAU;UAClB7D,KAAK,EAAE6D,UAAU,CAAC7D,KAAK;UACvBC,MAAM,EAAE4D,UAAU,CAAC5D,MAAM;UACzBoB,SAAS,EAAEwC,UAAU,CAACE,UAAU,CAAC,IAAI,CAAC,CAACzC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEuC,UAAU,CAAC7D,KAAK,EAAE6D,UAAU,CAAC5D,MAAM;QAC/F,CAAC;MACH;;MAEA;MACAtC,QAAQ,CAAC,MAAM;QACbqG,iBAAiB,CAAC;MACpB,CAAC;IACH;;IAEA;IACA,MAAMJ,oBAAoB,GAAGA,CAAA,KAAM;MACjCrF,GAAG,CAAC+B,IAAI,CAAC;MACT/B,GAAG,CAACkC,WAAW,GAAG,KAAI;MACtBlC,GAAG,CAACmC,SAAS,GAAG;MAChBnC,GAAG,CAAC0F,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;;MAExB,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,EAAEkB,CAAC,EAAE,EAAE;QACtD,MAAMC,CAAC,GAAGD,CAAC,IAAI/B,QAAQ,CAACW,KAAK,GAAGV,OAAO,CAACU,KAAK;QAC7CZ,GAAG,CAACoC,UAAU,CAACH,CAAC,EAAE,CAAC,EAAEhC,QAAQ,CAACW,KAAK,EAAEX,QAAQ,CAACW,KAAK;MACrD;MAEAZ,GAAG,CAACyC,OAAO,CAAC;;MAEZ;MACAkD,UAAU,CAAC,MAAM;QACf9D,cAAc,CAAC;MACjB,CAAC,EAAE,IAAI;IACT;;IAEA;IACA,MAAM4D,iBAAiB,GAAGA,CAAA,KAAM;MAC9BhF,WAAW,CAACG,KAAK,CAACgF,OAAO,CAAC,CAACC,QAAQ,EAAEX,KAAK,KAAK;QAC7C,MAAMY,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,yBAAyBd,KAAK,IAAI;QAC3E,IAAIY,SAAS,EAAE;UACb,MAAM9F,GAAG,GAAG8F,SAAS,CAACN,UAAU,CAAC,IAAI;;UAErC;UACAxF,GAAG,CAAC8B,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEgE,SAAS,CAACrE,KAAK,EAAEqE,SAAS,CAACpE,MAAM;;UAErD;UACA1B,GAAG,CAACgF,YAAY,CAACa,QAAQ,CAAC/C,SAAS,EAAE,CAAC,EAAE,CAAC;QAC3C;MACF,CAAC;IACH;;IAEA;IACA,MAAMyC,wBAAwB,GAAIL,KAAK,IAAK;MAC1C;MACA,MAAMe,UAAU,GAAGF,QAAQ,CAACG,aAAa,CAAC,QAAQ;MAClD,MAAMC,OAAO,GAAGF,UAAU,CAACT,UAAU,CAAC,IAAI;;MAE1C;MACA,MAAMlE,GAAG,GAAGC,MAAM,CAACC,gBAAgB,IAAI;;MAEvC;MACAyE,UAAU,CAACxE,KAAK,GAAGxB,QAAQ,CAACW,KAAK,GAAGU,GAAE;MACtC2E,UAAU,CAACvE,MAAM,GAAGzB,QAAQ,CAACW,KAAK,GAAGU,GAAE;;MAEvC;MACA2E,UAAU,CAACtE,KAAK,CAACF,KAAK,GAAGxB,QAAQ,CAACW,KAAK,GAAG,IAAG;MAC7CqF,UAAU,CAACtE,KAAK,CAACD,MAAM,GAAGzB,QAAQ,CAACW,KAAK,GAAG,IAAG;;MAE9C;MACAuF,OAAO,CAACvE,KAAK,CAACN,GAAG,EAAEA,GAAG;;MAEtB;MACA6E,OAAO,CAACvD,SAAS,GAAG,OAAM;MAC1BuD,OAAO,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEnG,QAAQ,CAACW,KAAK,EAAEX,QAAQ,CAACW,KAAK;;MAErD;MACA,MAAMyF,cAAc,GAAGnB,KAAK,IAAIjF,QAAQ,CAACW,KAAK,GAAGV,OAAO,CAACU,KAAK;MAC9D,MAAM0F,cAAc,GAAG;MACvB,MAAMC,YAAY,GAAGtG,QAAQ,CAACW,KAAI;MAClC,MAAM4F,aAAa,GAAGvG,QAAQ,CAACW,KAAI;;MAEnC;MACA,MAAM6F,aAAa,GAAGJ,cAAc,GAAG/E,GAAE;MACzC,MAAMoF,aAAa,GAAGJ,cAAc,GAAGhF,GAAE;MACzC,MAAMqF,WAAW,GAAGJ,YAAY,GAAGjF,GAAE;MACrC,MAAMsF,YAAY,GAAGJ,aAAa,GAAGlF,GAAE;MAEvCiC,OAAO,CAACsD,GAAG,CAAC,OAAO3B,KAAK,GAAG,CAAC,GAAG,EAAE;QAC/BA,KAAK,EAAEA,KAAK;QACZmB,cAAc,EAAEA,cAAc;QAC9BC,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA,YAAY;QAC1BC,aAAa,EAAEA,aAAa;QAC5BC,aAAa,EAAEA,aAAa;QAC5BC,aAAa,EAAEA,aAAa;QAC5BC,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA,YAAY;QAC1BtF,GAAG,EAAEA,GAAG;QACRwF,iBAAiB,EAAE/G,MAAM,CAACa,KAAK,CAACa,KAAK;QACrCsF,kBAAkB,EAAEhH,MAAM,CAACa,KAAK,CAACc,MAAM;QACvCsF,kBAAkB,EAAEjH,MAAM,CAACa,KAAK,CAACe,KAAK,CAACF,KAAK;QAC5CwF,mBAAmB,EAAElH,MAAM,CAACa,KAAK,CAACe,KAAK,CAACD;MAC1C,CAAC;;MAED;MACA;MACAyE,OAAO,CAACe,SAAS,CACfnH,MAAM,CAACa,KAAK,EACZ6F,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,YAAY;MAAG;MAC1D,CAAC,EAAE,CAAC,EAAEL,YAAY,EAAEC,aAAa,CAAyB;MAC5D;MAEA,OAAOP,UAAS;IAClB;;IAEA;IACA,MAAMkB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAIrH,aAAa,CAACc,KAAK,EAAE;MAEzBd,aAAa,CAACc,KAAK,GAAG,IAAG;MACzBjB,cAAc,CAACiB,KAAK,GAAG,EAAC;MACxBhB,QAAQ,CAACgB,KAAK,GAAG,IAAG;MACpBL,WAAW,CAACK,KAAK,GAAG,EAAC;MACrBJ,mBAAmB,CAACI,KAAK,GAAG,CAAC;;MAE7B;MACAwE,eAAe,CAAC;MAEhB,IAAI;QACF;QACA,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACuB,MAAM,EAAEkB,CAAC,EAAE,EAAE;UACtDxB,mBAAmB,CAACI,KAAK,CAACoB,CAAC,CAAC,GAAG,aAAY;UAE3C,IAAI;YACFuB,OAAO,CAACsD,GAAG,CAAC,QAAQ7E,CAAC,GAAG,CAAC,QAAQrB,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACyC,CAAC,CAAC,EAAE;YAC5D,MAAMd,MAAM,GAAG,MAAMkG,oBAAoB,CAACpF,CAAC;YAC3CzB,WAAW,CAACK,KAAK,CAACoB,CAAC,CAAC,GAAGd,MAAK;YAC5BV,mBAAmB,CAACI,KAAK,CAACoB,CAAC,CAAC,GAAG,WAAU;YACzCuB,OAAO,CAACsD,GAAG,CAAC,IAAI7E,CAAC,GAAG,CAAC,UAAU,EAAEd,MAAM;;YAEvC;YACA,MAAM,IAAImG,OAAO,CAACC,OAAO,IAAI3B,UAAU,CAAC2B,OAAO,EAAE,GAAG,CAAC;UAEvD,CAAC,CAAC,OAAOhE,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,IAAItB,CAAC,GAAG,CAAC,UAAU,EAAEsB,KAAK;YACxC/C,WAAW,CAACK,KAAK,CAACoB,CAAC,CAAC,GAAG;cACrBuF,UAAU,EAAE,EAAE;cACdC,QAAQ,EAAE7G,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACyC,CAAC,CAAC;cACnCb,SAAS,EAAE;YACb;YACAX,mBAAmB,CAACI,KAAK,CAACoB,CAAC,CAAC,GAAG,WAAU;UAC3C;QACF;;QAEA;QACA,MAAMyF,YAAY,GAAGlH,WAAW,CAACK,KAAK,CAAC8G,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxG,SAAS,CAAC,CAACL,MAAK;QACrE,MAAM8G,UAAU,GAAGrH,WAAW,CAACK,KAAK,CAACE,MAAK;QAC1C,MAAM+G,UAAU,GAAGJ,YAAY,KAAKG,UAAS;QAE7C,IAAIE,aAAa,GAAG,EAAC;QACrB,IAAID,UAAU,EAAE;UACdC,aAAa,GAAG,aAAavH,WAAW,CAACK,KAAK,CAACmH,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACJ,UAAU,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC,EAAC;QACjF,CAAC,MAAM;UACLF,aAAa,GAAG,QAAQL,YAAY,IAAIG,UAAU,KAAI;UACtD;UACA,MAAMK,OAAO,GAAG1H,WAAW,CAACK,KAAK,CAACmH,GAAG,CAAC,CAACJ,CAAC,EAAE3F,CAAC,KACzC,GAAGrB,WAAW,CAACC,KAAK,CAACrB,IAAI,CAACyC,CAAC,CAAC,IAAI2F,CAAC,CAACJ,UAAU,GAAGI,CAAC,CAACxG,SAAS,GAAG,GAAG,GAAG,GAAG,EACxE,CAAC,CAAC6G,IAAI,CAAC,GAAG;UACVF,aAAa,IAAI,QAAQG,OAAO,EAAC;QACnC;QAEArI,QAAQ,CAACgB,KAAK,GAAG;UACfO,SAAS,EAAE0G,UAAU;UACrBK,OAAO,EAAEJ;QACX;QAEA,IAAID,UAAU,EAAE;UACdlC,UAAU,CAAC,MAAM;YACf/F,QAAQ,CAACgB,KAAK,GAAG;cACfO,SAAS,EAAE,IAAI;cACf+G,OAAO,EAAE;YACX;UACF,CAAC,EAAE,IAAI;QACT;MAEF,CAAC,CAAC,OAAO5E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5B1D,QAAQ,CAACgB,KAAK,GAAG;UACfO,SAAS,EAAE,KAAK;UAChB+G,OAAO,EAAE;QACX;MACF,CAAC,SAAS;QACRpI,aAAa,CAACc,KAAK,GAAG,KAAI;MAC5B;IACF;;IAEA;IACA,MAAMwG,oBAAoB,GAAG,MAAOlC,KAAK,IAAK;MAC5C,IAAI;QACF;QACA,MAAMI,UAAU,GAAGC,wBAAwB,CAACL,KAAK;QAEjD,MAAMhE,MAAM,GAAG,MAAM7B,SAAS,CAAC8I,SAAS,CACtC7C,UAAU,EACV,SAAS;QAAE;QACX;UACE8C,MAAM,EAAGC,CAAC,IAAK;YACb,IAAIA,CAAC,CAACC,MAAM,KAAK,kBAAkB,EAAE;cACnC/E,OAAO,CAACsD,GAAG,CAAC,KAAK3B,KAAK,GAAG,CAAC,SAASqD,IAAI,CAACC,KAAK,CAACH,CAAC,CAACI,QAAQ,GAAG,GAAG,CAAC,GAAG;YACpE;UACF,CAAC;UACDC,qBAAqB,EAAErJ,SAAS,CAACsJ,GAAG,CAACC,WAAW;UAAE;UAClDC,uBAAuB,EAAElI,WAAW,CAACC,KAAK,CAACrB,IAAI;UAAE;UACjDuJ,yBAAyB,EAAE;QAC7B,CACF;QAEA,IAAInJ,cAAc,GAAGuB,MAAM,CAAC6H,IAAI,CAACxJ,IAAI,CAACyJ,IAAI,CAAC;;QAE3C;QACArJ,cAAc,GAAGA,cAAc,CAACsJ,OAAO,CAAC,mBAAmB,EAAE,EAAE;;QAE/D;QACA,MAAMC,YAAY,GAAGvI,WAAW,CAACC,KAAK,CAACrB,IAAI,CAAC2F,KAAK;QACjD,MAAM/D,SAAS,GAAGxB,cAAc,KAAKuJ,YAAW;QAEhD,OAAO;UACL3B,UAAU,EAAE5H,cAAc;UAC1B6H,QAAQ,EAAE0B,YAAY;UACtB/H,SAAS,EAAEA;QACb;MAEF,CAAC,CAAC,OAAOmC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,KAAK4B,KAAK,GAAG,CAAC,OAAO,EAAE5B,KAAK;QAC1C,MAAMA,KAAI;MACZ;IACF;;IAEA;IACA,MAAM6F,QAAQ,GAAGA,CAAA,KAAM;MACrBzJ,YAAY,CAACkB,KAAK,GAAG,CAAClB,YAAY,CAACkB,KAAK,GAAG,CAAC,IAAItB,KAAK,CAACsB,KAAK,CAACE,MAAK;IACnE;;IAEA;;IAEA;IACA5B,SAAS,CAAC,MAAM;MACd;MACAc,GAAG,GAAGD,MAAM,CAACa,KAAK,CAAC4E,UAAU,CAAC,IAAI;;MAElC;MACAxF,GAAG,CAACmC,SAAS,GAAG,CAAC,EAAC;MAClBnC,GAAG,CAAC0C,OAAO,GAAG,OAAO,EAAC;MACtB1C,GAAG,CAAC2C,QAAQ,GAAG,OAAO,EAAC;MACvB3C,GAAG,CAACkC,WAAW,GAAG,SAAS,EAAC;MAC5BlC,GAAG,CAAC4C,SAAS,GAAG,SAAS,EAAC;;MAE1B;MACAxB,YAAY,CAAC;;MAEb;MACAS,cAAc,CAAC;IACjB,CAAC;;IAED;;IAEA;IACA1C,KAAK,CAACwB,WAAW,EAAE,MAAM;MACvBgE,cAAc,CAAC;IACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}