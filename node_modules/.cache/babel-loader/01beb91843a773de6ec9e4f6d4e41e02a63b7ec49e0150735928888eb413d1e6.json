{"ast": null, "code": "'use strict';\n\n/**\n * readFromBlobOrFile\n *\n * @name readFromBlobOrFile\n * @function\n * @access private\n */\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.map.js\");\nrequire(\"core-js/modules/web.dom-exception.stack.js\");\nconst readFromBlobOrFile = blob => new Promise((resolve, reject) => {\n  const fileReader = new FileReader();\n  fileReader.onload = () => {\n    resolve(fileReader.result);\n  };\n  fileReader.onerror = ({\n    target: {\n      error: {\n        code\n      }\n    }\n  }) => {\n    reject(Error(`File could not be read! Code=${code}`));\n  };\n  fileReader.readAsArrayBuffer(blob);\n});\n\n/**\n * loadImage\n *\n * @name loadImage\n * @function load image from different source\n * @access private\n */\nconst loadImage = async image => {\n  let data = image;\n  if (typeof image === 'undefined') {\n    return 'undefined';\n  }\n  if (typeof image === 'string') {\n    // Base64 Image\n    if (/data:image\\/([a-zA-Z]*);base64,([^\"]*)/.test(image)) {\n      data = atob(image.split(',')[1]).split('').map(c => c.charCodeAt(0));\n    } else {\n      const resp = await fetch(image);\n      data = await resp.arrayBuffer();\n    }\n  } else if (typeof HTMLElement !== 'undefined' && image instanceof HTMLElement) {\n    if (image.tagName === 'IMG') {\n      data = await loadImage(image.src);\n    }\n    if (image.tagName === 'VIDEO') {\n      data = await loadImage(image.poster);\n    }\n    if (image.tagName === 'CANVAS') {\n      await new Promise(resolve => {\n        image.toBlob(async blob => {\n          data = await readFromBlobOrFile(blob);\n          resolve();\n        });\n      });\n    }\n  } else if (typeof OffscreenCanvas !== 'undefined' && image instanceof OffscreenCanvas) {\n    const blob = await image.convertToBlob();\n    data = await readFromBlobOrFile(blob);\n  } else if (image instanceof File || image instanceof Blob) {\n    data = await readFromBlobOrFile(image);\n  }\n  return new Uint8Array(data);\n};\nmodule.exports = loadImage;", "map": {"version": 3, "names": ["require", "readFromBlobOrFile", "blob", "Promise", "resolve", "reject", "fileReader", "FileReader", "onload", "result", "onerror", "target", "error", "code", "Error", "readAsA<PERSON>y<PERSON><PERSON>er", "loadImage", "image", "data", "test", "atob", "split", "map", "c", "charCodeAt", "resp", "fetch", "arrayBuffer", "HTMLElement", "tagName", "src", "poster", "toBlob", "OffscreenCanvas", "convertToBlob", "File", "Blob", "Uint8Array", "module", "exports"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/worker/browser/loadImage.js"], "sourcesContent": ["'use strict';\n\n/**\n * readFromBlobOrFile\n *\n * @name readFromBlobOrFile\n * @function\n * @access private\n */\nconst readFromBlobOrFile = (blob) => (\n  new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n      resolve(fileReader.result);\n    };\n    fileReader.onerror = ({ target: { error: { code } } }) => {\n      reject(Error(`File could not be read! Code=${code}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n  })\n);\n\n/**\n * loadImage\n *\n * @name loadImage\n * @function load image from different source\n * @access private\n */\nconst loadImage = async (image) => {\n  let data = image;\n  if (typeof image === 'undefined') {\n    return 'undefined';\n  }\n\n  if (typeof image === 'string') {\n    // Base64 Image\n    if (/data:image\\/([a-zA-Z]*);base64,([^\"]*)/.test(image)) {\n      data = atob(image.split(',')[1])\n        .split('')\n        .map((c) => c.charCodeAt(0));\n    } else {\n      const resp = await fetch(image);\n      data = await resp.arrayBuffer();\n    }\n  } else if (typeof HTMLElement !== 'undefined' && image instanceof HTMLElement) {\n    if (image.tagName === 'IMG') {\n      data = await loadImage(image.src);\n    }\n    if (image.tagName === 'VIDEO') {\n      data = await loadImage(image.poster);\n    }\n    if (image.tagName === 'CANVAS') {\n      await new Promise((resolve) => {\n        image.toBlob(async (blob) => {\n          data = await readFromBlobOrFile(blob);\n          resolve();\n        });\n      });\n    }\n  } else if (typeof OffscreenCanvas !== 'undefined' && image instanceof OffscreenCanvas) {\n    const blob = await image.convertToBlob();\n    data = await readFromBlobOrFile(blob);\n  } else if (image instanceof File || image instanceof Blob) {\n    data = await readFromBlobOrFile(image);\n  }\n\n  return new Uint8Array(data);\n};\n\nmodule.exports = loadImage;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AANAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAOA,MAAMC,kBAAkB,GAAIC,IAAI,IAC9B,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;EAC/B,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;EACnCD,UAAU,CAACE,MAAM,GAAG,MAAM;IACxBJ,OAAO,CAACE,UAAU,CAACG,MAAM,CAAC;EAC5B,CAAC;EACDH,UAAU,CAACI,OAAO,GAAG,CAAC;IAAEC,MAAM,EAAE;MAAEC,KAAK,EAAE;QAAEC;MAAK;IAAE;EAAE,CAAC,KAAK;IACxDR,MAAM,CAACS,KAAK,CAAC,gCAAgCD,IAAI,EAAE,CAAC,CAAC;EACvD,CAAC;EACDP,UAAU,CAACS,iBAAiB,CAACb,IAAI,CAAC;AACpC,CAAC,CACF;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,SAAS,GAAG,MAAOC,KAAK,IAAK;EACjC,IAAIC,IAAI,GAAGD,KAAK;EAChB,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IAChC,OAAO,WAAW;EACpB;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B;IACA,IAAI,wCAAwC,CAACE,IAAI,CAACF,KAAK,CAAC,EAAE;MACxDC,IAAI,GAAGE,IAAI,CAACH,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7BA,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,MAAM;MACL,MAAMC,IAAI,GAAG,MAAMC,KAAK,CAACT,KAAK,CAAC;MAC/BC,IAAI,GAAG,MAAMO,IAAI,CAACE,WAAW,CAAC,CAAC;IACjC;EACF,CAAC,MAAM,IAAI,OAAOC,WAAW,KAAK,WAAW,IAAIX,KAAK,YAAYW,WAAW,EAAE;IAC7E,IAAIX,KAAK,CAACY,OAAO,KAAK,KAAK,EAAE;MAC3BX,IAAI,GAAG,MAAMF,SAAS,CAACC,KAAK,CAACa,GAAG,CAAC;IACnC;IACA,IAAIb,KAAK,CAACY,OAAO,KAAK,OAAO,EAAE;MAC7BX,IAAI,GAAG,MAAMF,SAAS,CAACC,KAAK,CAACc,MAAM,CAAC;IACtC;IACA,IAAId,KAAK,CAACY,OAAO,KAAK,QAAQ,EAAE;MAC9B,MAAM,IAAI1B,OAAO,CAAEC,OAAO,IAAK;QAC7Ba,KAAK,CAACe,MAAM,CAAC,MAAO9B,IAAI,IAAK;UAC3BgB,IAAI,GAAG,MAAMjB,kBAAkB,CAACC,IAAI,CAAC;UACrCE,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,MAAM,IAAI,OAAO6B,eAAe,KAAK,WAAW,IAAIhB,KAAK,YAAYgB,eAAe,EAAE;IACrF,MAAM/B,IAAI,GAAG,MAAMe,KAAK,CAACiB,aAAa,CAAC,CAAC;IACxChB,IAAI,GAAG,MAAMjB,kBAAkB,CAACC,IAAI,CAAC;EACvC,CAAC,MAAM,IAAIe,KAAK,YAAYkB,IAAI,IAAIlB,KAAK,YAAYmB,IAAI,EAAE;IACzDlB,IAAI,GAAG,MAAMjB,kBAAkB,CAACgB,KAAK,CAAC;EACxC;EAEA,OAAO,IAAIoB,UAAU,CAACnB,IAAI,CAAC;AAC7B,CAAC;AAEDoB,MAAM,CAACC,OAAO,GAAGvB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}