{"ast": null, "code": "'use strict';\n\n/**\n * spawnWorker\n *\n * @name spawnWorker\n * @function create a new Worker in browser\n * @access public\n */\nmodule.exports = ({\n  workerPath,\n  workerBlobURL\n}) => {\n  let worker;\n  if (Blob && URL && workerBlobURL) {\n    const blob = new Blob([`importScripts(\"${workerPath}\");`], {\n      type: 'application/javascript'\n    });\n    worker = new Worker(URL.createObjectURL(blob));\n  } else {\n    worker = new Worker(workerPath);\n  }\n  return worker;\n};", "map": {"version": 3, "names": ["module", "exports", "worker<PERSON><PERSON>", "workerBlobURL", "worker", "Blob", "URL", "blob", "type", "Worker", "createObjectURL"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/worker/browser/spawnWorker.js"], "sourcesContent": ["'use strict';\n\n/**\n * spawnWorker\n *\n * @name spawnWorker\n * @function create a new Worker in browser\n * @access public\n */\nmodule.exports = ({ workerPath, workerBlobURL }) => {\n  let worker;\n  if (Blob && URL && workerBlobURL) {\n    const blob = new Blob([`importScripts(\"${workerPath}\");`], {\n      type: 'application/javascript',\n    });\n    worker = new Worker(URL.createObjectURL(blob));\n  } else {\n    worker = new Worker(workerPath);\n  }\n\n  return worker;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,CAAC;EAAEC,UAAU;EAAEC;AAAc,CAAC,KAAK;EAClD,IAAIC,MAAM;EACV,IAAIC,IAAI,IAAIC,GAAG,IAAIH,aAAa,EAAE;IAChC,MAAMI,IAAI,GAAG,IAAIF,IAAI,CAAC,CAAC,kBAAkBH,UAAU,KAAK,CAAC,EAAE;MACzDM,IAAI,EAAE;IACR,CAAC,CAAC;IACFJ,MAAM,GAAG,IAAIK,MAAM,CAACH,GAAG,CAACI,eAAe,CAACH,IAAI,CAAC,CAAC;EAChD,CAAC,MAAM;IACLH,MAAM,GAAG,IAAIK,MAAM,CAACP,UAAU,CAAC;EACjC;EAEA,OAAOE,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}