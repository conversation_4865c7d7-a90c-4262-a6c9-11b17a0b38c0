{"ast": null, "code": "'use strict';\n\nmodule.exports = key => {\n  const env = {};\n  if (typeof WorkerGlobalScope !== 'undefined') {\n    env.type = 'webworker';\n  } else if (typeof document === 'object') {\n    env.type = 'browser';\n  } else if (typeof process === 'object' && typeof require === 'function') {\n    env.type = 'node';\n  }\n  if (typeof key === 'undefined') {\n    return env;\n  }\n  return env[key];\n};", "map": {"version": 3, "names": ["module", "exports", "key", "env", "WorkerGlobalScope", "type", "document", "process", "require"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/utils/getEnvironment.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (key) => {\n  const env = {};\n\n  if (typeof WorkerGlobalScope !== 'undefined') {\n    env.type = 'webworker';\n  } else if (typeof document === 'object') {\n    env.type = 'browser';\n  } else if (typeof process === 'object' && typeof require === 'function') {\n    env.type = 'node';\n  }\n\n  if (typeof key === 'undefined') {\n    return env;\n  }\n\n  return env[key];\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAIC,GAAG,IAAK;EACxB,MAAMC,GAAG,GAAG,CAAC,CAAC;EAEd,IAAI,OAAOC,iBAAiB,KAAK,WAAW,EAAE;IAC5CD,GAAG,CAACE,IAAI,GAAG,WAAW;EACxB,CAAC,MAAM,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;IACvCH,GAAG,CAACE,IAAI,GAAG,SAAS;EACtB,CAAC,MAAM,IAAI,OAAOE,OAAO,KAAK,QAAQ,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;IACvEL,GAAG,CAACE,IAAI,GAAG,MAAM;EACnB;EAEA,IAAI,OAAOH,GAAG,KAAK,WAAW,EAAE;IAC9B,OAAOC,GAAG;EACZ;EAEA,OAAOA,GAAG,CAACD,GAAG,CAAC;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}