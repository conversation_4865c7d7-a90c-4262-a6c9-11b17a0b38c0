{"ast": null, "code": "'use strict';\n\n/**\n * terminateWorker\n *\n * @name terminateWorker\n * @function terminate worker\n * @access public\n */\nmodule.exports = worker => {\n  worker.terminate();\n};", "map": {"version": 3, "names": ["module", "exports", "worker", "terminate"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/worker/browser/terminateWorker.js"], "sourcesContent": ["'use strict';\n\n/**\n * terminateWorker\n *\n * @name terminateWorker\n * @function terminate worker\n * @access public\n */\nmodule.exports = (worker) => {\n  worker.terminate();\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAIC,MAAM,IAAK;EAC3BA,MAAM,CAACC,SAAS,CAAC,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}