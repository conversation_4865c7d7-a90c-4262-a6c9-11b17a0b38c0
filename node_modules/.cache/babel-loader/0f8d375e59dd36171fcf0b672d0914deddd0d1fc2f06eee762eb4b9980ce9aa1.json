{"ast": null, "code": "'use strict';\n\nmodule.exports = (prefix, cnt) => `${prefix}-${cnt}-${Math.random().toString(16).slice(3, 8)}`;", "map": {"version": 3, "names": ["module", "exports", "prefix", "cnt", "Math", "random", "toString", "slice"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/utils/getId.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (prefix, cnt) => (\n  `${prefix}-${cnt}-${Math.random().toString(16).slice(3, 8)}`\n);\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,CAACC,MAAM,EAAEC,GAAG,KAC3B,GAAGD,MAAM,IAAIC,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}