{"ast": null, "code": "'use strict';\n\n/**\n *\n * Tesseract Worker adapter for browser\n *\n * @fileoverview Tesseract Worker adapter for browser\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nconst defaultOptions = require('./defaultOptions');\nconst spawnWorker = require('./spawnWorker');\nconst terminateWorker = require('./terminateWorker');\nconst onMessage = require('./onMessage');\nconst send = require('./send');\nconst loadImage = require('./loadImage');\nmodule.exports = {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  send,\n  loadImage\n};", "map": {"version": 3, "names": ["defaultOptions", "require", "spawnWorker", "terminateWorker", "onMessage", "send", "loadImage", "module", "exports"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/worker/browser/index.js"], "sourcesContent": ["'use strict';\n\n/**\n *\n * Tesseract Worker adapter for browser\n *\n * @fileoverview Tesseract Worker adapter for browser\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nconst defaultOptions = require('./defaultOptions');\nconst spawnWorker = require('./spawnWorker');\nconst terminateWorker = require('./terminateWorker');\nconst onMessage = require('./onMessage');\nconst send = require('./send');\nconst loadImage = require('./loadImage');\n\nmodule.exports = {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  send,\n  loadImage,\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAClD,MAAMC,WAAW,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC5C,MAAME,eAAe,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AACpD,MAAMG,SAAS,GAAGH,OAAO,CAAC,aAAa,CAAC;AACxC,MAAMI,IAAI,GAAGJ,OAAO,CAAC,QAAQ,CAAC;AAC9B,MAAMK,SAAS,GAAGL,OAAO,CAAC,aAAa,CAAC;AAExCM,MAAM,CAACC,OAAO,GAAG;EACfR,cAAc;EACdE,WAAW;EACXC,eAAe;EACfC,SAAS;EACTC,IAAI;EACJC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}