{"ast": null, "code": "'use strict';\n\nconst createWorker = require('./createWorker');\nconst recognize = async (image, langs, options) => {\n  const worker = await createWorker(langs, 1, options);\n  return worker.recognize(image).finally(async () => {\n    await worker.terminate();\n  });\n};\nconst detect = async (image, options) => {\n  const worker = await createWorker('osd', 0, options);\n  return worker.detect(image).finally(async () => {\n    await worker.terminate();\n  });\n};\nmodule.exports = {\n  recognize,\n  detect\n};", "map": {"version": 3, "names": ["createWorker", "require", "recognize", "image", "langs", "options", "worker", "finally", "terminate", "detect", "module", "exports"], "sources": ["/Volumes/Data/Project/VUE/xx/node_modules/tesseract.js/src/Tesseract.js"], "sourcesContent": ["'use strict';\n\nconst createWorker = require('./createWorker');\n\nconst recognize = async (image, langs, options) => {\n  const worker = await createWorker(langs, 1, options);\n  return worker.recognize(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nconst detect = async (image, options) => {\n  const worker = await createWorker('osd', 0, options);\n  return worker.detect(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nmodule.exports = {\n  recognize,\n  detect,\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,YAAY,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAE9C,MAAMC,SAAS,GAAG,MAAAA,CAAOC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACjD,MAAMC,MAAM,GAAG,MAAMN,YAAY,CAACI,KAAK,EAAE,CAAC,EAAEC,OAAO,CAAC;EACpD,OAAOC,MAAM,CAACJ,SAAS,CAACC,KAAK,CAAC,CAC3BI,OAAO,CAAC,YAAY;IACnB,MAAMD,MAAM,CAACE,SAAS,CAAC,CAAC;EAC1B,CAAC,CAAC;AACN,CAAC;AAED,MAAMC,MAAM,GAAG,MAAAA,CAAON,KAAK,EAAEE,OAAO,KAAK;EACvC,MAAMC,MAAM,GAAG,MAAMN,YAAY,CAAC,KAAK,EAAE,CAAC,EAAEK,OAAO,CAAC;EACpD,OAAOC,MAAM,CAACG,MAAM,CAACN,KAAK,CAAC,CACxBI,OAAO,CAAC,YAAY;IACnB,MAAMD,MAAM,CAACE,SAAS,CAAC,CAAC;EAC1B,CAAC,CAAC;AACN,CAAC;AAEDE,MAAM,CAACC,OAAO,GAAG;EACfT,SAAS;EACTO;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}