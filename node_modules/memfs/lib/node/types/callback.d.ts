/// <reference types="node" />
/// <reference types="node" />
import type { PathLike, symlink } from 'fs';
import type * as misc from './misc';
import type * as opts from './options';
export interface FsCallbackApi {
    open(path: PathLike, flags: misc.TFlags, /* ... */ callback: misc.TCallback<number>): any;
    open(path: PathLike, flags: misc.TFlags, mode: misc.TMode, callback: misc.TCallback<number>): any;
    close(fd: number, callback: misc.TCallback<void>): void;
    read(fd: number, buffer: Buffer | ArrayBufferView | DataView, offset: number, length: number, position: number, callback: (err?: Error | null, bytesRead?: number, buffer?: Buffer | ArrayBufferView | DataView) => void): void;
    readFile(id: misc.TFileId, callback: misc.TCallback<misc.TDataOut>): any;
    readFile(id: misc.TFileId, options: opts.IReadFileOptions | string, callback: misc.TCallback<misc.TDataOut>): any;
    write(fd: number, buffer: Buffer | ArrayBufferView | DataView, callback: (...args: any[]) => void): any;
    write(fd: number, buffer: Buffer | ArrayBufferView | DataView, offset: number, callback: (...args: any[]) => void): any;
    write(fd: number, buffer: Buffer | ArrayBufferView | DataView, offset: number, length: number, callback: (...args: any[]) => void): any;
    write(fd: number, buffer: Buffer | ArrayBufferView | DataView, offset: number, length: number, position: number, callback: (...args: any[]) => void): any;
    write(fd: number, str: string, callback: (...args: any[]) => void): any;
    write(fd: number, str: string, position: number, callback: (...args: any[]) => void): any;
    write(fd: number, str: string, position: number, encoding: BufferEncoding, callback: (...args: any[]) => void): any;
    writeFile(id: misc.TFileId, data: misc.TData, callback: misc.TCallback<void>): any;
    writeFile(id: misc.TFileId, data: misc.TData, options: opts.IWriteFileOptions | string, callback: misc.TCallback<void>): any;
    copyFile(src: PathLike, dest: PathLike, callback: misc.TCallback<void>): any;
    copyFile(src: PathLike, dest: PathLike, flags: misc.TFlagsCopy, callback: misc.TCallback<void>): any;
    link(existingPath: PathLike, newPath: PathLike, callback: misc.TCallback<void>): void;
    unlink(path: PathLike, callback: misc.TCallback<void>): void;
    symlink(target: PathLike, path: PathLike, callback: misc.TCallback<void>): any;
    symlink(target: PathLike, path: PathLike, type: symlink.Type, callback: misc.TCallback<void>): any;
    realpath(path: PathLike, callback: misc.TCallback<misc.TDataOut>): any;
    realpath(path: PathLike, options: opts.IRealpathOptions | string, callback: misc.TCallback<misc.TDataOut>): any;
    lstat(path: PathLike, callback: misc.TCallback<misc.IStats>): void;
    lstat(path: PathLike, options: opts.IStatOptions, callback: misc.TCallback<misc.IStats>): void;
    stat(path: PathLike, callback: misc.TCallback<misc.IStats>): void;
    stat(path: PathLike, options: opts.IStatOptions, callback: misc.TCallback<misc.IStats>): void;
    fstat(fd: number, callback: misc.TCallback<misc.IStats>): void;
    fstat(fd: number, options: opts.IFStatOptions, callback: misc.TCallback<misc.IStats>): void;
    rename(oldPath: PathLike, newPath: PathLike, callback: misc.TCallback<void>): void;
    exists(path: PathLike, callback: (exists: boolean) => void): void;
    access(path: PathLike, callback: misc.TCallback<void>): any;
    access(path: PathLike, mode: number, callback: misc.TCallback<void>): any;
    appendFile(id: misc.TFileId, data: misc.TData, callback: misc.TCallback<void>): any;
    appendFile(id: misc.TFileId, data: misc.TData, options: opts.IAppendFileOptions | string, callback: misc.TCallback<void>): any;
    readdir(path: PathLike, callback: misc.TCallback<misc.TDataOut[] | misc.IDirent[]>): any;
    readdir(path: PathLike, options: opts.IReaddirOptions | string, callback: misc.TCallback<misc.TDataOut[] | misc.IDirent[]>): any;
    readlink(path: PathLike, callback: misc.TCallback<misc.TDataOut>): any;
    readlink(path: PathLike, options: opts.IOptions, callback: misc.TCallback<misc.TDataOut>): any;
    fsyncSync(fd: number): void;
    fsync(fd: number, callback: misc.TCallback<void>): void;
    fdatasync(fd: number, callback: misc.TCallback<void>): void;
    ftruncate(fd: number, callback: misc.TCallback<void>): any;
    ftruncate(fd: number, len: number, callback: misc.TCallback<void>): any;
    truncate(id: misc.TFileId, callback: misc.TCallback<void>): any;
    truncate(id: misc.TFileId, len: number, callback: misc.TCallback<void>): any;
    futimes(fd: number, atime: misc.TTime, mtime: misc.TTime, callback: misc.TCallback<void>): void;
    utimes(path: PathLike, atime: misc.TTime, mtime: misc.TTime, callback: misc.TCallback<void>): void;
    mkdir(path: PathLike, callback: misc.TCallback<void>): any;
    mkdir(path: PathLike, mode: misc.TMode | (opts.IMkdirOptions & {
        recursive?: false;
    }), callback: misc.TCallback<void>): any;
    mkdir(path: PathLike, mode: opts.IMkdirOptions & {
        recursive: true;
    }, callback: misc.TCallback<string>): any;
    mkdir(path: PathLike, mode: misc.TMode | opts.IMkdirOptions, callback: misc.TCallback<string>): any;
    mkdirp(path: PathLike, callback: misc.TCallback<string>): any;
    mkdirp(path: PathLike, mode: misc.TMode, callback: misc.TCallback<string>): any;
    mkdtemp(prefix: string, callback: misc.TCallback<void>): void;
    mkdtemp(prefix: string, options: opts.IOptions, callback: misc.TCallback<void>): any;
    rmdir(path: PathLike, callback: misc.TCallback<void>): any;
    rmdir(path: PathLike, options: opts.IRmdirOptions, callback: misc.TCallback<void>): any;
    rm(path: PathLike, callback: misc.TCallback<void>): void;
    rm(path: PathLike, options: opts.IRmOptions, callback: misc.TCallback<void>): void;
    fchmod(fd: number, mode: misc.TMode, callback: misc.TCallback<void>): void;
    chmod(path: PathLike, mode: misc.TMode, callback: misc.TCallback<void>): void;
    lchmod(path: PathLike, mode: misc.TMode, callback: misc.TCallback<void>): void;
    fchown(fd: number, uid: number, gid: number, callback: misc.TCallback<void>): void;
    chown(path: PathLike, uid: number, gid: number, callback: misc.TCallback<void>): void;
    lchown(path: PathLike, uid: number, gid: number, callback: misc.TCallback<void>): void;
    watchFile(path: PathLike, listener: (curr: misc.IStats, prev: misc.IStats) => void): misc.IStatWatcher;
    watchFile(path: PathLike, options: opts.IWatchFileOptions, listener: (curr: misc.IStats, prev: misc.IStats) => void): misc.IStatWatcher;
    unwatchFile(path: PathLike, listener?: (curr: misc.IStats, prev: misc.IStats) => void): void;
    createReadStream(path: PathLike, options?: opts.IReadStreamOptions | string): misc.IReadStream;
    createWriteStream(path: PathLike, options?: opts.IWriteStreamOptions | string): misc.IWriteStream;
    watch(path: PathLike, options?: opts.IWatchOptions | string, listener?: (eventType: string, filename: string) => void): misc.IFSWatcher;
}
