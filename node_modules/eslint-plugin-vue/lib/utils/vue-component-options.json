{"nuxt": ["asyncData", "fetch", "head", "key", "layout", "loading", "middleware", "scrollToTop", "transition", "validate", "watch<PERSON><PERSON>y"], "vue-router": ["beforeRouteEnter", "beforeRouteUpdate", "beforeRouteLeave"], "vue": ["data", "props", "propsData", "computed", "methods", "watch", "el", "template", "render", "renderError", "staticRenderFns", "beforeCreate", "created", "<PERSON><PERSON><PERSON><PERSON>", "destroyed", "beforeMount", "mounted", "beforeUpdate", "updated", "activated", "deactivated", "errorCaptured", "serverPrefetch", "directives", "components", "transitions", "filters", "provide", "inject", "model", "parent", "mixins", "name", "extends", "delimiters", "comments", "inheritAttrs", "setup", "emits", "beforeUnmount", "unmounted", "renderTracked", "renderTriggered"]}