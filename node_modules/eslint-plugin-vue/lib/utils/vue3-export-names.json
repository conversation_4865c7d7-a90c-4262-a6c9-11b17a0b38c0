["compile", "createApp", "createSSRApp", "defineCustomElement", "defineSSRCustomElement", "hydrate", "render", "Transition", "TransitionGroup", "TransitionGroupProps", "TransitionProps", "useCssModule", "useCssVars", "vModelCheckbox", "vModelDynamic", "vModelRadio", "vModelSelect", "vModelText", "vShow", "<PERSON>ue<PERSON>lement", "VueElementConstructor", "<PERSON><PERSON><PERSON><PERSON>", "withModifiers", "AllowedComponentProps", "App", "AppConfig", "AppContext", "AsyncComponent<PERSON><PERSON>der", "AsyncComponentOptions", "BaseTransition", "BaseTransitionProps", "callWithAsyncErrorHandling", "callWithErrorHandling", "camelize", "capitalize", "cloneVNode", "Comment", "CompatVue", "Component", "ComponentCustomOptions", "ComponentCustomProperties", "ComponentCustomProps", "ComponentInternalInstance", "ComponentObjectPropsOptions", "ComponentOptions", "ComponentOptionsBase", "ComponentOptionsMixin", "ComponentOptionsWithArrayProps", "ComponentOptionsWithObjectProps", "ComponentOptionsWithoutProps", "ComponentPropsOptions", "ComponentPublicInstance", "computed", "ComputedGetter", "ComputedOptions", "ComputedRef", "ComputedSetter", "ConcreteComponent", "CreateAppFunction", "createBlock", "createCommentVNode", "CreateComponentPublicInstance", "createElementBlock", "createElementVNode", "createHydrationRenderer", "<PERSON><PERSON><PERSON><PERSON>", "createSlots", "createStaticVNode", "createTextVNode", "createVNode", "customRef", "CustomRefFactory", "DebuggerEvent", "DebuggerEventExtraInfo", "DebuggerOptions", "DeepReadonly", "defineAsyncComponent", "DefineComponent", "defineComponent", "defineEmits", "defineExpose", "defineProps", "DeprecationTypes", "devtools", "Directive", "DirectiveArguments", "DirectiveBinding", "DirectiveHook", "effect", "EffectScheduler", "EffectScope", "effectScope", "EmitsOptions", "ErrorCodes", "ExtractDefaultPropTypes", "ExtractPropTypes", "Fragment", "FunctionalComponent", "FunctionDirective", "getCurrentInstance", "getCurrentScope", "getTransitionRawChildren", "guardReactiveProps", "h", "handleError", "HMRRuntime", "Hydration<PERSON><PERSON><PERSON>", "initCustomFormatter", "inject", "Injection<PERSON>ey", "isMemoSame", "isProxy", "isReactive", "is<PERSON><PERSON><PERSON>ly", "isRef", "isRuntimeOnly", "isShallow", "isVNode", "KeepAlive", "KeepAliveProps", "LegacyConfig", "mark<PERSON>aw", "mergeProps", "MethodOptions", "nextTick", "normalizeClass", "normalizeProps", "normalizeStyle", "ObjectDirective", "ObjectEmitsOptions", "onActivated", "onBeforeMount", "onBeforeUnmount", "onBeforeUpdate", "onDeactivated", "onErrorCaptured", "onMounted", "onRenderTracked", "onRenderTriggered", "onScopeDispose", "onServerPrefetch", "onUnmounted", "onUpdated", "openBlock", "OptionMergeFunction", "Plugin", "popScopeId", "Prop", "PropType", "provide", "proxyRefs", "pushScopeId", "queuePostFlushCb", "reactive", "ReactiveEffect", "ReactiveEffectOptions", "ReactiveEffectRunner", "ReactiveFlags", "readonly", "Ref", "ref", "registerRuntimeCompiler", "<PERSON><PERSON><PERSON>", "RendererElement", "RendererNode", "RendererOptions", "RenderFunction", "renderList", "renderSlot", "resolveComponent", "resolveDirective", "resolveDynamicComponent", "resolveTransitionHooks", "RootHydrateFunction", "RootRenderFunction", "RuntimeCompilerOptions", "setBlockTracking", "setDevtoolsHook", "setTransitionHooks", "SetupContext", "ShallowReactive", "shallowReactive", "shallowReadonly", "ShallowRef", "shallowRef", "ShallowUnwrapRef", "Slot", "Slots", "ssrContextKey", "Static", "stop", "Suspense", "SuspenseBoundary", "SuspenseProps", "Teleport", "TeleportProps", "Text", "toDisplayString", "toHandlerKey", "toHandlers", "toRaw", "ToRef", "toRef", "ToRefs", "toRefs", "TrackOpTypes", "transformVNodeArgs", "TransitionHooks", "TransitionState", "TriggerOpTypes", "triggerRef", "unref", "UnwrapNestedRefs", "UnwrapRef", "useAttrs", "useSlots", "useSSRContext", "useTransitionState", "version", "VNode", "VNodeArrayChildren", "VNodeChild", "VNodeNormalizedChildren", "VNodeProps", "VNodeTypes", "warn", "watch", "WatchCallback", "WatchEffect", "watchEffect", "WatchOptions", "WatchOptionsBase", "watchPostEffect", "WatchSource", "WatchStopHandle", "watchSyncEffect", "withCtx", "with<PERSON><PERSON><PERSON><PERSON>", "withDirectives", "withMemo", "withScopeId", "WritableComputedOptions", "WritableComputedRef", "CSSProperties", "StyleValue", "HTMLAttributes", "AnchorHTMLAttributes", "AreaHTMLAttributes", "AudioHTMLAttributes", "BaseHTMLAttributes", "BlockquoteHTMLAttributes", "ButtonHTMLAttributes", "CanvasHTMLAttributes", "ColHTMLAttributes", "ColgroupHTMLAttributes", "DataHTMLAttributes", "DetailsHTMLAttributes", "DelHTMLAttributes", "DialogHTMLAttributes", "EmbedHTMLAttributes", "FieldsetHTMLAttributes", "FormHTMLAttributes", "HtmlHTMLAttributes", "IframeHTMLAttributes", "ImgHTMLAttributes", "InsHTMLAttributes", "InputHTMLAttributes", "KeygenHTMLAttributes", "LabelHTMLAttributes", "LiHTMLAttributes", "LinkHTMLAttributes", "MapHTMLAttributes", "MenuHTMLAttributes", "MediaHTMLAttributes", "MetaHTMLAttributes", "MeterHTMLAttributes", "QuoteHTMLAttributes", "ObjectHTMLAttributes", "OlHTMLAttributes", "OptgroupHTMLAttributes", "OptionHTMLAttributes", "OutputHTMLAttributes", "ParamHTMLAttributes", "ProgressHTMLAttributes", "ScriptHTMLAttributes", "SelectHTMLAttributes", "SourceHTMLAttributes", "StyleHTMLAttributes", "TableHTMLAttributes", "TextareaHTMLAttributes", "TdHTMLAttributes", "ThHTMLAttributes", "TimeHTMLAttributes", "TrackHTMLAttributes", "VideoHTMLAttributes", "WebViewHTMLAttributes", "SVGAttributes", "Events"]