/*! For license information please see worker.min.js.LICENSE.txt */
(()=>{var t={30:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}var i=function(t){"use strict";var e,r=Object.prototype,i=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",h=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var i=e&&e.prototype instanceof v?e:v,a=Object.create(i.prototype),s=new _(n||[]);return o(a,"_invoke",{value:U(t,r,s)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var p="suspendedStart",y="suspendedYield",d="executing",g="completed",b={};function v(){}function w(){}function m(){}var A={};f(A,s,(function(){return this}));var E=Object.getPrototypeOf,k=E&&E(E(j([])));k&&k!==r&&i.call(k,s)&&(A=k);var x=m.prototype=v.prototype=Object.create(A);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){function r(o,a,s,u){var h=l(t[o],t,a);if("throw"!==h.type){var f=h.arg,c=f.value;return c&&"object"===n(c)&&i.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,s,u)}),(function(t){r("throw",t,s,u)})):e.resolve(c).then((function(t){f.value=t,s(f)}),(function(t){return r("throw",t,s,u)}))}u(h.arg)}var a;o(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function U(t,e,r){var n=p;return function(i,o){if(n===d)throw new Error("Generator is already running");if(n===g){if("throw"===i)throw o;return P()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var s=S(a,r);if(s){if(s===b)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===p)throw n=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=d;var u=l(t,e,r);if("normal"===u.type){if(n=r.done?g:y,u.arg===b)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=g,r.method="throw",r.arg=u.arg)}}}function S(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var o=l(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,b;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,b):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function B(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function _(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function j(t){if(t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(i.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}return{next:P}}function P(){return{value:e,done:!0}}return w.prototype=m,o(x,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:w,configurable:!0}),w.displayName=f(m,h,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,f(t,h,"GeneratorFunction")),t.prototype=Object.create(x),t},t.awrap=function(t){return{__await:t}},O(I.prototype),f(I.prototype,u,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new I(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(x),f(x,h,"Generator"),f(x,s,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=j,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(B),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var u=i.call(a,"catchLoc"),h=i.call(a,"finallyLoc");if(u&&h){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!h)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),B(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;B(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:j(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),b}},t}("object"===n(t=r.nmd(t))?t.exports:{});try{regeneratorRuntime=i}catch(t){"object"===("undefined"==typeof globalThis?"undefined":n(globalThis))?globalThis.regeneratorRuntime=i:Function("r","regeneratorRuntime = r")(i)}},54:t=>{"use strict";t.exports={COLOR:0,GREY:1,BINARY:2}},65:function(t,e){"use strict";var r=this,n=!1;e.logging=n,e.setLogging=function(t){n=t},e.log=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return n?console.log.apply(r,e):null}},133:function(t,e,r){var n=r(545).hp;(function(){"use strict";function t(t){throw t}var r=void 0,i=!0,o="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array&&"undefined"!=typeof DataView;function a(e,r){this.index="number"==typeof r?r:0,this.m=0,this.buffer=e instanceof(o?Uint8Array:Array)?e:new(o?Uint8Array:Array)(32768),2*this.buffer.length<=this.index&&t(Error("invalid index")),this.buffer.length<=this.index&&this.f()}a.prototype.f=function(){var t,e=this.buffer,r=e.length,n=new(o?Uint8Array:Array)(r<<1);if(o)n.set(e);else for(t=0;t<r;++t)n[t]=e[t];return this.buffer=n},a.prototype.d=function(t,e,r){var n,i=this.buffer,o=this.index,a=this.m,s=i[o];if(r&&1<e&&(t=8<e?(l[255&t]<<24|l[t>>>8&255]<<16|l[t>>>16&255]<<8|l[t>>>24&255])>>32-e:l[t]>>8-e),8>e+a)s=s<<e|t,a+=e;else for(n=0;n<e;++n)s=s<<1|t>>e-n-1&1,8==++a&&(a=0,i[o++]=l[s],s=0,o===i.length&&(i=this.f()));i[o]=s,this.buffer=i,this.m=a,this.index=o},a.prototype.finish=function(){var t,e=this.buffer,r=this.index;return 0<this.m&&(e[r]<<=8-this.m,e[r]=l[e[r]],r++),o?t=e.subarray(0,r):(e.length=r,t=e),t};var s,u=new(o?Uint8Array:Array)(256);for(s=0;256>s;++s){for(var h=c=s,f=7,c=c>>>1;c;c>>>=1)h<<=1,h|=1&c,--f;u[s]=(h<<f&255)>>>0}var l=u;function p(t,e,r){var n,i="number"==typeof e?e:e=0,o="number"==typeof r?r:t.length;for(n=-1,i=7&o;i--;++e)n=n>>>8^d[255&(n^t[e])];for(i=o>>3;i--;e+=8)n=(n=(n=(n=(n=(n=(n=(n=n>>>8^d[255&(n^t[e])])>>>8^d[255&(n^t[e+1])])>>>8^d[255&(n^t[e+2])])>>>8^d[255&(n^t[e+3])])>>>8^d[255&(n^t[e+4])])>>>8^d[255&(n^t[e+5])])>>>8^d[255&(n^t[e+6])])>>>8^d[255&(n^t[e+7])];return(4294967295^n)>>>0}var y=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],d=o?new Uint32Array(y):y;function g(){}function b(t){this.buffer=new(o?Uint16Array:Array)(2*t),this.length=0}function v(t){var e,r,n,i,a,s,u,h,f,c,l=t.length,p=0,y=Number.POSITIVE_INFINITY;for(h=0;h<l;++h)t[h]>p&&(p=t[h]),t[h]<y&&(y=t[h]);for(e=1<<p,r=new(o?Uint32Array:Array)(e),n=1,i=0,a=2;n<=p;){for(h=0;h<l;++h)if(t[h]===n){for(s=0,u=i,f=0;f<n;++f)s=s<<1|1&u,u>>=1;for(c=n<<16|h,f=s;f<e;f+=a)r[f]=c;++i}++n,i<<=1,a<<=1}return[r,p,y]}function w(t,e){this.k=A,this.F=0,this.input=o&&t instanceof Array?new Uint8Array(t):t,this.b=0,e&&(e.lazy&&(this.F=e.lazy),"number"==typeof e.compressionType&&(this.k=e.compressionType),e.outputBuffer&&(this.a=o&&e.outputBuffer instanceof Array?new Uint8Array(e.outputBuffer):e.outputBuffer),"number"==typeof e.outputIndex&&(this.b=e.outputIndex)),this.a||(this.a=new(o?Uint8Array:Array)(32768))}b.prototype.getParent=function(t){return 2*((t-2)/4|0)},b.prototype.push=function(t,e){var r,n,i,o=this.buffer;for(r=this.length,o[this.length++]=e,o[this.length++]=t;0<r&&(n=this.getParent(r),o[r]>o[n]);)i=o[r],o[r]=o[n],o[n]=i,i=o[r+1],o[r+1]=o[n+1],o[n+1]=i,r=n;return this.length},b.prototype.pop=function(){var t,e,r,n,i,o=this.buffer;for(e=o[0],t=o[1],this.length-=2,o[0]=o[this.length],o[1]=o[this.length+1],i=0;!((n=2*i+2)>=this.length)&&(n+2<this.length&&o[n+2]>o[n]&&(n+=2),o[n]>o[i]);)r=o[i],o[i]=o[n],o[n]=r,r=o[i+1],o[i+1]=o[n+1],o[n+1]=r,i=n;return{index:t,value:e,length:this.length}};var m,A=2,E={NONE:0,L:1,t:A,X:3},k=[];for(m=0;288>m;m++)switch(i){case 143>=m:k.push([m+48,8]);break;case 255>=m:k.push([m-144+400,9]);break;case 279>=m:k.push([m-256+0,7]);break;case 287>=m:k.push([m-280+192,8]);break;default:t("invalid literal: "+m)}function x(t,e){this.length=t,this.N=e}w.prototype.h=function(){var e,n,s,u,h=this.input;switch(this.k){case 0:for(s=0,u=h.length;s<u;){var f,c,l,p=n=o?h.subarray(s,s+65535):h.slice(s,s+65535),y=(s+=n.length)===u,d=r,g=r,b=this.a,v=this.b;if(o){for(b=new Uint8Array(this.a.buffer);b.length<=v+p.length+5;)b=new Uint8Array(b.length<<1);b.set(this.a)}if(f=y?1:0,b[v++]=0|f,l=65536+~(c=p.length)&65535,b[v++]=255&c,b[v++]=c>>>8&255,b[v++]=255&l,b[v++]=l>>>8&255,o)b.set(p,v),v+=p.length,b=b.subarray(0,v);else{for(d=0,g=p.length;d<g;++d)b[v++]=p[d];b.length=v}this.b=v,this.a=b}break;case 1:var w=new a(o?new Uint8Array(this.a.buffer):this.a,this.b);w.d(1,1,i),w.d(1,2,i);var m,E,x,O=U(this,h);for(m=0,E=O.length;m<E;m++)if(x=O[m],a.prototype.d.apply(w,k[x]),256<x)w.d(O[++m],O[++m],i),w.d(O[++m],5),w.d(O[++m],O[++m],i);else if(256===x)break;this.a=w.finish(),this.b=this.a.length;break;case A:var I,S,_,j,P,T,R,C,M,N,F,G,z,D,W,Y=new a(o?new Uint8Array(this.a.buffer):this.a,this.b),V=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],q=Array(19);for(I=A,Y.d(1,1,i),Y.d(I,2,i),S=U(this,h),R=B(T=L(this.U,15)),M=B(C=L(this.T,7)),_=286;257<_&&0===T[_-1];_--);for(j=30;1<j&&0===C[j-1];j--);var J,Q,X,H,K,$,Z=_,tt=j,et=new(o?Uint32Array:Array)(Z+tt),rt=new(o?Uint32Array:Array)(316),nt=new(o?Uint8Array:Array)(19);for(J=Q=0;J<Z;J++)et[Q++]=T[J];for(J=0;J<tt;J++)et[Q++]=C[J];if(!o)for(J=0,H=nt.length;J<H;++J)nt[J]=0;for(J=K=0,H=et.length;J<H;J+=Q){for(Q=1;J+Q<H&&et[J+Q]===et[J];++Q);if(X=Q,0===et[J])if(3>X)for(;0<X--;)rt[K++]=0,nt[0]++;else for(;0<X;)($=138>X?X:138)>X-3&&$<X&&($=X-3),10>=$?(rt[K++]=17,rt[K++]=$-3,nt[17]++):(rt[K++]=18,rt[K++]=$-11,nt[18]++),X-=$;else if(rt[K++]=et[J],nt[et[J]]++,3>--X)for(;0<X--;)rt[K++]=et[J],nt[et[J]]++;else for(;0<X;)($=6>X?X:6)>X-3&&$<X&&($=X-3),rt[K++]=16,rt[K++]=$-3,nt[16]++,X-=$}for(e=o?rt.subarray(0,K):rt.slice(0,K),N=L(nt,7),D=0;19>D;D++)q[D]=N[V[D]];for(P=19;4<P&&0===q[P-1];P--);for(F=B(N),Y.d(_-257,5,i),Y.d(j-1,5,i),Y.d(P-4,4,i),D=0;D<P;D++)Y.d(q[D],3,i);for(D=0,W=e.length;D<W;D++)if(G=e[D],Y.d(F[G],N[G],i),16<=G){switch(D++,G){case 16:z=2;break;case 17:z=3;break;case 18:z=7;break;default:t("invalid code: "+G)}Y.d(e[D],z,i)}var it,ot,at,st,ut,ht,ft,ct,lt=[R,T],pt=[M,C];for(ut=lt[0],ht=lt[1],ft=pt[0],ct=pt[1],it=0,ot=S.length;it<ot;++it)if(at=S[it],Y.d(ut[at],ht[at],i),256<at)Y.d(S[++it],S[++it],i),st=S[++it],Y.d(ft[st],ct[st],i),Y.d(S[++it],S[++it],i);else if(256===at)break;this.a=Y.finish(),this.b=this.a.length;break;default:t("invalid compression type")}return this.a};var O=function(){function e(e){switch(i){case 3===e:return[257,e-3,0];case 4===e:return[258,e-4,0];case 5===e:return[259,e-5,0];case 6===e:return[260,e-6,0];case 7===e:return[261,e-7,0];case 8===e:return[262,e-8,0];case 9===e:return[263,e-9,0];case 10===e:return[264,e-10,0];case 12>=e:return[265,e-11,1];case 14>=e:return[266,e-13,1];case 16>=e:return[267,e-15,1];case 18>=e:return[268,e-17,1];case 22>=e:return[269,e-19,2];case 26>=e:return[270,e-23,2];case 30>=e:return[271,e-27,2];case 34>=e:return[272,e-31,2];case 42>=e:return[273,e-35,3];case 50>=e:return[274,e-43,3];case 58>=e:return[275,e-51,3];case 66>=e:return[276,e-59,3];case 82>=e:return[277,e-67,4];case 98>=e:return[278,e-83,4];case 114>=e:return[279,e-99,4];case 130>=e:return[280,e-115,4];case 162>=e:return[281,e-131,5];case 194>=e:return[282,e-163,5];case 226>=e:return[283,e-195,5];case 257>=e:return[284,e-227,5];case 258===e:return[285,e-258,0];default:t("invalid length: "+e)}}var r,n,o=[];for(r=3;258>=r;r++)n=e(r),o[r]=n[2]<<24|n[1]<<16|n[0];return o}(),I=o?new Uint32Array(O):O;function U(e,n){function a(e,r){var n,o,a,s,u=e.N,h=[],f=0;switch(n=I[e.length],h[f++]=65535&n,h[f++]=n>>16&255,h[f++]=n>>24,i){case 1===u:o=[0,u-1,0];break;case 2===u:o=[1,u-2,0];break;case 3===u:o=[2,u-3,0];break;case 4===u:o=[3,u-4,0];break;case 6>=u:o=[4,u-5,1];break;case 8>=u:o=[5,u-7,1];break;case 12>=u:o=[6,u-9,2];break;case 16>=u:o=[7,u-13,2];break;case 24>=u:o=[8,u-17,3];break;case 32>=u:o=[9,u-25,3];break;case 48>=u:o=[10,u-33,4];break;case 64>=u:o=[11,u-49,4];break;case 96>=u:o=[12,u-65,5];break;case 128>=u:o=[13,u-97,5];break;case 192>=u:o=[14,u-129,6];break;case 256>=u:o=[15,u-193,6];break;case 384>=u:o=[16,u-257,7];break;case 512>=u:o=[17,u-385,7];break;case 768>=u:o=[18,u-513,8];break;case 1024>=u:o=[19,u-769,8];break;case 1536>=u:o=[20,u-1025,9];break;case 2048>=u:o=[21,u-1537,9];break;case 3072>=u:o=[22,u-2049,10];break;case 4096>=u:o=[23,u-3073,10];break;case 6144>=u:o=[24,u-4097,11];break;case 8192>=u:o=[25,u-6145,11];break;case 12288>=u:o=[26,u-8193,12];break;case 16384>=u:o=[27,u-12289,12];break;case 24576>=u:o=[28,u-16385,13];break;case 32768>=u:o=[29,u-24577,13];break;default:t("invalid distance")}for(n=o,h[f++]=n[0],h[f++]=n[1],h[f++]=n[2],a=0,s=h.length;a<s;++a)b[v++]=h[a];m[h[0]]++,A[h[3]]++,w=e.length+r-1,y=null}var s,u,h,f,c,l,p,y,d,g={},b=o?new Uint16Array(2*n.length):[],v=0,w=0,m=new(o?Uint32Array:Array)(286),A=new(o?Uint32Array:Array)(30),E=e.F;if(!o){for(h=0;285>=h;)m[h++]=0;for(h=0;29>=h;)A[h++]=0}for(m[256]=1,s=0,u=n.length;s<u;++s){for(h=c=0,f=3;h<f&&s+h!==u;++h)c=c<<8|n[s+h];if(g[c]===r&&(g[c]=[]),l=g[c],!(0<w--)){for(;0<l.length&&32768<s-l[0];)l.shift();if(s+3>=u){for(y&&a(y,-1),h=0,f=u-s;h<f;++h)d=n[s+h],b[v++]=d,++m[d];break}0<l.length?(p=S(n,s,l),y?y.length<p.length?(d=n[s-1],b[v++]=d,++m[d],a(p,0)):a(y,-1):p.length<E?y=p:a(p,0)):y?a(y,-1):(d=n[s],b[v++]=d,++m[d])}l.push(s)}return b[v++]=256,m[256]++,e.U=m,e.T=A,o?b.subarray(0,v):b}function S(t,e,r){var n,i,o,a,s,u,h=0,f=t.length;a=0,u=r.length;t:for(;a<u;a++){if(n=r[u-a-1],o=3,3<h){for(s=h;3<s;s--)if(t[n+s-1]!==t[e+s-1])continue t;o=h}for(;258>o&&e+o<f&&t[n+o]===t[e+o];)++o;if(o>h&&(i=n,h=o),258===o)break}return new x(h,e-i)}function L(t,e){var r,n,i,a,s,u=t.length,h=new b(572),f=new(o?Uint8Array:Array)(u);if(!o)for(a=0;a<u;a++)f[a]=0;for(a=0;a<u;++a)0<t[a]&&h.push(a,t[a]);if(r=Array(h.length/2),n=new(o?Uint32Array:Array)(h.length/2),1===r.length)return f[h.pop().index]=1,f;for(a=0,s=h.length/2;a<s;++a)r[a]=h.pop(),n[a]=r[a].value;for(i=function(t,e,r){function n(t){var r=y[t][d[t]];r===e?(n(t+1),n(t+1)):--l[r],++d[t]}var i,a,s,u,h,f=new(o?Uint16Array:Array)(r),c=new(o?Uint8Array:Array)(r),l=new(o?Uint8Array:Array)(e),p=Array(r),y=Array(r),d=Array(r),g=(1<<r)-e,b=1<<r-1;for(f[r-1]=e,a=0;a<r;++a)g<b?c[a]=0:(c[a]=1,g-=b),g<<=1,f[r-2-a]=(f[r-1-a]/2|0)+e;for(f[0]=c[0],p[0]=Array(f[0]),y[0]=Array(f[0]),a=1;a<r;++a)f[a]>2*f[a-1]+c[a]&&(f[a]=2*f[a-1]+c[a]),p[a]=Array(f[a]),y[a]=Array(f[a]);for(i=0;i<e;++i)l[i]=r;for(s=0;s<f[r-1];++s)p[r-1][s]=t[s],y[r-1][s]=s;for(i=0;i<r;++i)d[i]=0;for(1===c[r-1]&&(--l[0],++d[r-1]),a=r-2;0<=a;--a){for(u=i=0,h=d[a+1],s=0;s<f[a];s++)(u=p[a+1][h]+p[a+1][h+1])>t[i]?(p[a][s]=u,y[a][s]=e,h+=2):(p[a][s]=t[i],y[a][s]=i,++i);d[a]=0,1===c[a]&&n(a)}return l}(n,n.length,e),a=0,s=r.length;a<s;++a)f[r[a].index]=i[a];return f}function B(t){var e,r,n,i,a=new(o?Uint16Array:Array)(t.length),s=[],u=[],h=0;for(e=0,r=t.length;e<r;e++)s[t[e]]=1+(0|s[t[e]]);for(e=1,r=16;e<=r;e++)u[e]=h,h+=0|s[e],h<<=1;for(e=0,r=t.length;e<r;e++)for(h=u[t[e]],u[t[e]]+=1,n=a[e]=0,i=t[e];n<i;n++)a[e]=a[e]<<1|1&h,h>>>=1;return a}function _(t,e){this.input=t,this.b=this.c=0,this.g={},e&&(e.flags&&(this.g=e.flags),"string"==typeof e.filename&&(this.filename=e.filename),"string"==typeof e.comment&&(this.w=e.comment),e.deflateOptions&&(this.l=e.deflateOptions)),this.l||(this.l={})}_.prototype.h=function(){var t,e,n,i,a,s,u,h,f=new(o?Uint8Array:Array)(32768),c=0,l=this.input,y=this.c,d=this.filename,g=this.w;if(f[c++]=31,f[c++]=139,f[c++]=8,t=0,this.g.fname&&(t|=T),this.g.fcomment&&(t|=R),this.g.fhcrc&&(t|=P),f[c++]=t,e=(Date.now?Date.now():+new Date)/1e3|0,f[c++]=255&e,f[c++]=e>>>8&255,f[c++]=e>>>16&255,f[c++]=e>>>24&255,f[c++]=0,f[c++]=j,this.g.fname!==r){for(u=0,h=d.length;u<h;++u)255<(s=d.charCodeAt(u))&&(f[c++]=s>>>8&255),f[c++]=255&s;f[c++]=0}if(this.g.comment){for(u=0,h=g.length;u<h;++u)255<(s=g.charCodeAt(u))&&(f[c++]=s>>>8&255),f[c++]=255&s;f[c++]=0}return this.g.fhcrc&&(n=65535&p(f,0,c),f[c++]=255&n,f[c++]=n>>>8&255),this.l.outputBuffer=f,this.l.outputIndex=c,f=(a=new w(l,this.l)).h(),c=a.b,o&&(c+8>f.buffer.byteLength?(this.a=new Uint8Array(c+8),this.a.set(new Uint8Array(f.buffer)),f=this.a):f=new Uint8Array(f.buffer)),i=p(l,r,r),f[c++]=255&i,f[c++]=i>>>8&255,f[c++]=i>>>16&255,f[c++]=i>>>24&255,h=l.length,f[c++]=255&h,f[c++]=h>>>8&255,f[c++]=h>>>16&255,f[c++]=h>>>24&255,this.c=y,o&&c<f.length&&(this.a=f=f.subarray(0,c)),f};var j=255,P=2,T=8,R=16;function C(e,r){switch(this.o=[],this.p=32768,this.e=this.j=this.c=this.s=0,this.input=o?new Uint8Array(e):e,this.u=!1,this.q=N,this.K=!1,!r&&(r={})||(r.index&&(this.c=r.index),r.bufferSize&&(this.p=r.bufferSize),r.bufferType&&(this.q=r.bufferType),r.resize&&(this.K=r.resize)),this.q){case M:this.b=32768,this.a=new(o?Uint8Array:Array)(32768+this.p+258);break;case N:this.b=0,this.a=new(o?Uint8Array:Array)(this.p),this.f=this.S,this.z=this.O,this.r=this.Q;break;default:t(Error("invalid inflate mode"))}}var M=0,N=1;C.prototype.i=function(){for(;!this.u;){var e=nt(this,3);switch(1&e&&(this.u=i),e>>>=1){case 0:var n=this.input,a=this.c,s=this.a,u=this.b,h=n.length,f=r,c=s.length,l=r;switch(this.e=this.j=0,a+1>=h&&t(Error("invalid uncompressed block header: LEN")),f=n[a++]|n[a++]<<8,a+1>=h&&t(Error("invalid uncompressed block header: NLEN")),f===~(n[a++]|n[a++]<<8)&&t(Error("invalid uncompressed block header: length verify")),a+f>n.length&&t(Error("input buffer is broken")),this.q){case M:for(;u+f>s.length;){if(f-=l=c-u,o)s.set(n.subarray(a,a+l),u),u+=l,a+=l;else for(;l--;)s[u++]=n[a++];this.b=u,s=this.f(),u=this.b}break;case N:for(;u+f>s.length;)s=this.f({B:2});break;default:t(Error("invalid inflate mode"))}if(o)s.set(n.subarray(a,a+f),u),u+=f,a+=f;else for(;f--;)s[u++]=n[a++];this.c=a,this.b=u,this.a=s;break;case 1:this.r(tt,rt);break;case 2:var p,y,d,g,b=nt(this,5)+257,w=nt(this,5)+1,m=nt(this,4)+4,A=new(o?Uint8Array:Array)(D.length),E=r,k=r,x=r,O=r,I=r;for(I=0;I<m;++I)A[D[I]]=nt(this,3);if(!o)for(I=m,m=A.length;I<m;++I)A[D[I]]=0;for(p=v(A),E=new(o?Uint8Array:Array)(b+w),I=0,g=b+w;I<g;)switch(k=it(this,p),k){case 16:for(O=3+nt(this,2);O--;)E[I++]=x;break;case 17:for(O=3+nt(this,3);O--;)E[I++]=0;x=0;break;case 18:for(O=11+nt(this,7);O--;)E[I++]=0;x=0;break;default:x=E[I++]=k}y=v(o?E.subarray(0,b):E.slice(0,b)),d=v(o?E.subarray(b):E.slice(b)),this.r(y,d);break;default:t(Error("unknown BTYPE: "+e))}}return this.z()};var F,G,z=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],D=o?new Uint16Array(z):z,W=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],Y=o?new Uint16Array(W):W,V=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],q=o?new Uint8Array(V):V,J=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],Q=o?new Uint16Array(J):J,X=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],H=o?new Uint8Array(X):X,K=new(o?Uint8Array:Array)(288);for(F=0,G=K.length;F<G;++F)K[F]=143>=F?8:255>=F?9:279>=F?7:8;var $,Z,tt=v(K),et=new(o?Uint8Array:Array)(30);for($=0,Z=et.length;$<Z;++$)et[$]=5;var rt=v(et);function nt(e,r){for(var n,i=e.j,o=e.e,a=e.input,s=e.c,u=a.length;o<r;)s>=u&&t(Error("input buffer is broken")),i|=a[s++]<<o,o+=8;return n=i&(1<<r)-1,e.j=i>>>r,e.e=o-r,e.c=s,n}function it(e,r){for(var n,i,o=e.j,a=e.e,s=e.input,u=e.c,h=s.length,f=r[0],c=r[1];a<c&&!(u>=h);)o|=s[u++]<<a,a+=8;return(i=(n=f[o&(1<<c)-1])>>>16)>a&&t(Error("invalid code length: "+i)),e.j=o>>i,e.e=a-i,e.c=u,65535&n}function ot(t){this.input=t,this.c=0,this.G=[],this.R=!1}function at(t){if("string"==typeof t){var e,r,n=t.split("");for(e=0,r=n.length;e<r;e++)n[e]=(255&n[e].charCodeAt(0))>>>0;t=n}for(var i,o=1,a=0,s=t.length,u=0;0<s;){s-=i=1024<s?1024:s;do{a+=o+=t[u++]}while(--i);o%=65521,a%=65521}return(a<<16|o)>>>0}function st(e,r){var n,i;this.input=e,this.c=0,!r&&(r={})||(r.index&&(this.c=r.index),r.verify&&(this.V=r.verify)),n=e[this.c++],i=e[this.c++],(15&n)===ut?this.method=ut:t(Error("unsupported compression method")),0!=((n<<8)+i)%31&&t(Error("invalid fcheck flag:"+((n<<8)+i)%31)),32&i&&t(Error("fdict flag is not supported")),this.J=new C(e,{index:this.c,bufferSize:r.bufferSize,bufferType:r.bufferType,resize:r.resize})}C.prototype.r=function(t,e){var r=this.a,n=this.b;this.A=t;for(var i,o,a,s,u=r.length-258;256!==(i=it(this,t));)if(256>i)n>=u&&(this.b=n,r=this.f(),n=this.b),r[n++]=i;else for(s=Y[o=i-257],0<q[o]&&(s+=nt(this,q[o])),i=it(this,e),a=Q[i],0<H[i]&&(a+=nt(this,H[i])),n>=u&&(this.b=n,r=this.f(),n=this.b);s--;)r[n]=r[n++-a];for(;8<=this.e;)this.e-=8,this.c--;this.b=n},C.prototype.Q=function(t,e){var r=this.a,n=this.b;this.A=t;for(var i,o,a,s,u=r.length;256!==(i=it(this,t));)if(256>i)n>=u&&(u=(r=this.f()).length),r[n++]=i;else for(s=Y[o=i-257],0<q[o]&&(s+=nt(this,q[o])),i=it(this,e),a=Q[i],0<H[i]&&(a+=nt(this,H[i])),n+s>u&&(u=(r=this.f()).length);s--;)r[n]=r[n++-a];for(;8<=this.e;)this.e-=8,this.c--;this.b=n},C.prototype.f=function(){var t,e,r=new(o?Uint8Array:Array)(this.b-32768),n=this.b-32768,i=this.a;if(o)r.set(i.subarray(32768,r.length));else for(t=0,e=r.length;t<e;++t)r[t]=i[t+32768];if(this.o.push(r),this.s+=r.length,o)i.set(i.subarray(n,n+32768));else for(t=0;32768>t;++t)i[t]=i[n+t];return this.b=32768,i},C.prototype.S=function(t){var e,r,n,i=this.input.length/this.c+1|0,a=this.input,s=this.a;return t&&("number"==typeof t.B&&(i=t.B),"number"==typeof t.M&&(i+=t.M)),r=2>i?(n=(a.length-this.c)/this.A[2]/2*258|0)<s.length?s.length+n:s.length<<1:s.length*i,o?(e=new Uint8Array(r)).set(s):e=s,this.a=e},C.prototype.z=function(){var t,e,r,n,i,a=0,s=this.a,u=this.o,h=new(o?Uint8Array:Array)(this.s+(this.b-32768));if(0===u.length)return o?this.a.subarray(32768,this.b):this.a.slice(32768,this.b);for(e=0,r=u.length;e<r;++e)for(n=0,i=(t=u[e]).length;n<i;++n)h[a++]=t[n];for(e=32768,r=this.b;e<r;++e)h[a++]=s[e];return this.o=[],this.buffer=h},C.prototype.O=function(){var t,e=this.b;return o?this.K?(t=new Uint8Array(e)).set(this.a.subarray(0,e)):t=this.a.subarray(0,e):(this.a.length>e&&(this.a.length=e),t=this.a),this.buffer=t},ot.prototype.i=function(){for(var e=this.input.length;this.c<e;){var n,a,s=new g,u=r,h=r,f=r,c=r,l=r,y=r,d=r,b=this.input,v=this.c;if(s.C=b[v++],s.D=b[v++],(31!==s.C||139!==s.D)&&t(Error("invalid file signature:"+s.C+","+s.D)),s.v=b[v++],8===s.v||t(Error("unknown compression method: "+s.v)),s.n=b[v++],a=b[v++]|b[v++]<<8|b[v++]<<16|b[v++]<<24,s.$=new Date(1e3*a),s.ba=b[v++],s.aa=b[v++],0<(4&s.n)&&(s.W=b[v++]|b[v++]<<8,v+=s.W),0<(s.n&T)){for(y=[],l=0;0<(c=b[v++]);)y[l++]=String.fromCharCode(c);s.name=y.join("")}if(0<(s.n&R)){for(y=[],l=0;0<(c=b[v++]);)y[l++]=String.fromCharCode(c);s.w=y.join("")}0<(s.n&P)&&(s.P=65535&p(b,0,v),s.P!==(b[v++]|b[v++]<<8)&&t(Error("invalid header crc16"))),u=b[b.length-4]|b[b.length-3]<<8|b[b.length-2]<<16|b[b.length-1]<<24,b.length-v-4-4<512*u&&(f=u),h=new C(b,{index:v,bufferSize:f}),s.data=n=h.i(),v=h.c,s.Y=d=(b[v++]|b[v++]<<8|b[v++]<<16|b[v++]<<24)>>>0,p(n,r,r)!==d&&t(Error("invalid CRC-32 checksum: 0x"+p(n,r,r).toString(16)+" / 0x"+d.toString(16))),s.Z=u=(b[v++]|b[v++]<<8|b[v++]<<16|b[v++]<<24)>>>0,(4294967295&n.length)!==u&&t(Error("invalid input size: "+(4294967295&n.length)+" / "+u)),this.G.push(s),this.c=v}this.R=i;var w,m,A,E=this.G,k=0,x=0;for(w=0,m=E.length;w<m;++w)x+=E[w].data.length;if(o)for(A=new Uint8Array(x),w=0;w<m;++w)A.set(E[w].data,k),k+=E[w].data.length;else{for(A=[],w=0;w<m;++w)A[w]=E[w].data;A=Array.prototype.concat.apply([],A)}return A},st.prototype.i=function(){var e,r=this.input;return e=this.J.i(),this.c=this.J.c,this.V&&(r[this.c++]<<24|r[this.c++]<<16|r[this.c++]<<8|r[this.c++])>>>0!==at(e)&&t(Error("invalid adler-32 checksum")),e};var ut=8;function ht(t,e){this.input=t,this.a=new(o?Uint8Array:Array)(32768),this.k=ft.t;var r,n={};for(r in!e&&(e={})||"number"!=typeof e.compressionType||(this.k=e.compressionType),e)n[r]=e[r];n.outputBuffer=this.a,this.I=new w(this.input,n)}var ft=E;function ct(t,e){var r;return r=new ht(t).h(),e||(e={}),e.H?r:dt(r)}function lt(t,e){var r;return t.subarray=t.slice,r=new st(t).i(),e||(e={}),e.noBuffer?r:dt(r)}function pt(t,e){var r;return t.subarray=t.slice,r=new _(t).h(),e||(e={}),e.H?r:dt(r)}function yt(t,e){var r;return t.subarray=t.slice,r=new ot(t).i(),e||(e={}),e.H?r:dt(r)}function dt(t){var e,r,i=new n(t.length);for(e=0,r=t.length;e<r;++e)i[e]=t[e];return i}ht.prototype.h=function(){var e,r,n,i,a,s,u,h=0;if(u=this.a,(e=ut)===ut?r=Math.LOG2E*Math.log(32768)-8:t(Error("invalid compression method")),n=r<<4|e,u[h++]=n,e===ut)switch(this.k){case ft.NONE:a=0;break;case ft.L:a=1;break;case ft.t:a=2;break;default:t(Error("unsupported compression type"))}else t(Error("invalid compression method"));return i=a<<6,u[h++]=i|31-(256*n+i)%31,s=at(this.input),this.I.b=h,h=(u=this.I.h()).length,o&&((u=new Uint8Array(u.buffer)).length<=h+4&&(this.a=new Uint8Array(u.length+4),this.a.set(u),u=this.a),u=u.subarray(0,h+4)),u[h++]=s>>24&255,u[h++]=s>>16&255,u[h++]=s>>8&255,u[h++]=255&s,u},e.deflate=function(t,e,r){process.nextTick((function(){var n,i;try{i=ct(t,r)}catch(t){n=t}e(n,i)}))},e.deflateSync=ct,e.inflate=function(t,e,r){process.nextTick((function(){var n,i;try{i=lt(t,r)}catch(t){n=t}e(n,i)}))},e.inflateSync=lt,e.gzip=function(t,e,r){process.nextTick((function(){var n,i;try{i=pt(t,r)}catch(t){n=t}e(n,i)}))},e.gzipSync=pt,e.gunzip=function(t,e,r){process.nextTick((function(){var n,i;try{i=yt(t,r)}catch(t){n=t}e(n,i)}))},e.gunzipSync=yt}).call(this)},242:t=>{"use strict";const e={bigInt:()=>(async t=>{try{return(await WebAssembly.instantiate(t)).instance.exports.b(BigInt(0))===BigInt(0)}catch(t){return!1}})(new Uint8Array([0,97,115,109,1,0,0,0,1,6,1,96,1,126,1,126,3,2,1,0,7,5,1,1,98,0,0,10,6,1,4,0,32,0,11])),bulkMemory:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,3,1,0,1,10,14,1,12,0,65,0,65,0,65,0,252,10,0,0,11])),exceptions:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,8,1,6,0,6,64,25,11,11])),exceptionsFinal:()=>(async()=>{try{return new WebAssembly.Module(Uint8Array.from(atob("AGFzbQEAAAABBAFgAAADAgEAChABDgACaR9AAQMAAAsACxoL"),(t=>t.codePointAt(0)))),!0}catch(t){return!1}})(),extendedConst:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,5,3,1,0,1,11,9,1,0,65,1,65,2,106,11,0])),gc:()=>(async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,95,1,120,0])))(),jsStringBuiltins:()=>(async()=>{try{return await WebAssembly.instantiate(Uint8Array.from(atob("AGFzbQEAAAABBgFgAW8BfwIXAQ53YXNtOmpzLXN0cmluZwR0ZXN0AAA="),(t=>t.codePointAt(0))),{},{builtins:["js-string"]}),!0}catch(t){return!1}})(),jspi:()=>(async()=>"Suspending"in WebAssembly)(),memory64:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,5,3,1,4,1])),multiMemory:()=>(async()=>{try{return new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,5,5,2,0,0,0,0])),!0}catch(t){return!1}})(),multiValue:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,6,1,96,0,2,127,127,3,2,1,0,10,8,1,6,0,65,0,65,0,11])),mutableGlobals:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,2,8,1,1,97,1,98,3,127,1,6,6,1,127,1,65,0,11,7,5,1,1,97,3,1])),referenceTypes:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,7,1,5,0,208,112,26,11])),relaxedSimd:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,15,1,13,0,65,1,253,15,65,2,253,15,253,128,2,11])),saturatedFloatToInt:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,12,1,10,0,67,0,0,0,0,252,0,26,11])),signExtensions:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,8,1,6,0,65,0,192,26,11])),simd:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11])),streamingCompilation:()=>(async()=>"compileStreaming"in WebAssembly)(),tailCall:async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,6,1,4,0,18,0,11])),threads:()=>(async t=>{try{return"undefined"!=typeof MessageChannel&&(new MessageChannel).port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(t)}catch(t){return!1}})(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11])),typeReflection:()=>(async()=>"Function"in WebAssembly)(),typedFunctionReferences:()=>(async()=>{try{return new WebAssembly.Module(Uint8Array.from(atob("AGFzbQEAAAABEANgAX8Bf2ABZAABf2AAAX8DBAMBAAIJBQEDAAEBChwDCwBBCkEqIAAUAGoLBwAgAEEBagsGANIBEAAL"),(t=>t.codePointAt(0)))),!0}catch(t){return!1}})()};t.exports=e},258:(t,e,r)=>{"use strict";t.exports=r(133).gunzipSync},329:(t,e,r)=>{"use strict";var n=r(545).hp;function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=function(t){var e=function(t){if("object"!=i(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==i(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var u=r(450);t.exports=function(t,e,r){var i,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=66===r[0]&&77===r[1]||66===r[1]&&77===r[0],h=parseInt(null===(i=r.slice(0,500).join(" ").match(/1 18 0 3 0 0 0 1 0 (\d)/))||void 0===i?void 0:i[1],10)||1;if(s){var f=n.from(Array.from(a(a({},r),{},{length:Object.keys(r).length}))),c=u.decode(f);t.FS.writeFile("/input",u.encode(c).data)}else t.FS.writeFile("/input",r);if(1===e.SetImageFile(h,o))throw Error("Error attempting to read image.")}},330:t=>{"use strict";t.exports=JSON.parse('{"El":{"QE":"^6.0.0"}}')},334:(t,e,r)=>{"use strict";var n=r(535),i=r(54),o=function(t){var e=t.split("\n");if("  "===e[0].substring(0,2))for(var r=0;r<e.length;r+=1)"  "===e[r].substring(0,2)&&(e[r]=e[r].slice(2));return e.join("\n")};t.exports=function(t,e,r,a){var s,u,h,f,c,l=function(e,r){return Object.keys(t).filter((function(n){return n.startsWith("".concat(r,"_"))&&t[n]===e})).map((function(t){return t.slice(r.length+1)}))[0]},p=function(r){e.WriteImage(r,"/image.png");var i=t.FS.readFile("/image.png"),o="data:image/png;base64,".concat(n(i.buffer));return t.FS.unlink("/image.png"),o};return{text:r.text?e.GetUTF8Text():null,hocr:r.hocr?o(e.GetHOCRText()):null,tsv:r.tsv?e.GetTSVText():null,box:r.box?e.GetBoxText():null,unlv:r.unlv?e.GetUNLVText():null,osd:r.osd?e.GetOsdText():null,pdf:r.pdf?(h=null!==(s=a.pdfTitle)&&void 0!==s?s:"Tesseract OCR Result",f=null!==(u=a.pdfTextOnly)&&void 0!==u&&u,c=new t.TessPDFRenderer("tesseract-ocr","/",f),c.BeginDocument(h),c.AddImage(e),c.EndDocument(),t._free(c),t.FS.readFile("/tesseract-ocr.pdf")):null,imageColor:r.imageColor?p(i.COLOR):null,imageGrey:r.imageGrey?p(i.GREY):null,imageBinary:r.imageBinary?p(i.BINARY):null,confidence:a.skipRecognition?null:e.MeanTextConf(),blocks:r.blocks&&!a.skipRecognition?JSON.parse(e.GetJSONText()).blocks:null,layoutBlocks:r.layoutBlocks&&a.skipRecognition?JSON.parse(e.GetJSONText()).blocks:null,psm:l(e.GetPageSegMode(),"PSM"),oem:l(e.oem(),"OEM"),version:e.Version(),debug:r.debug?t.FS.readFile("/debugInternal.txt",{encoding:"utf8",flags:"a+"}):null}}},398:(t,e,r)=>{var n=r(545).hp;function i(t){this.buffer=t.data,this.width=t.width,this.height=t.height,this.extraBytes=this.width%4,this.rgbSize=this.height*(3*this.width+this.extraBytes),this.headerInfoSize=40,this.data=[],this.flag="BM",this.reserved=0,this.offset=54,this.fileSize=this.rgbSize+this.offset,this.planes=1,this.bitPP=24,this.compress=0,this.hr=0,this.vr=0,this.colors=0,this.importantColors=0}i.prototype.encode=function(){var t=new n(this.offset+this.rgbSize);this.pos=0,t.write(this.flag,this.pos,2),this.pos+=2,t.writeUInt32LE(this.fileSize,this.pos),this.pos+=4,t.writeUInt32LE(this.reserved,this.pos),this.pos+=4,t.writeUInt32LE(this.offset,this.pos),this.pos+=4,t.writeUInt32LE(this.headerInfoSize,this.pos),this.pos+=4,t.writeUInt32LE(this.width,this.pos),this.pos+=4,t.writeInt32LE(-this.height,this.pos),this.pos+=4,t.writeUInt16LE(this.planes,this.pos),this.pos+=2,t.writeUInt16LE(this.bitPP,this.pos),this.pos+=2,t.writeUInt32LE(this.compress,this.pos),this.pos+=4,t.writeUInt32LE(this.rgbSize,this.pos),this.pos+=4,t.writeUInt32LE(this.hr,this.pos),this.pos+=4,t.writeUInt32LE(this.vr,this.pos),this.pos+=4,t.writeUInt32LE(this.colors,this.pos),this.pos+=4,t.writeUInt32LE(this.importantColors,this.pos),this.pos+=4;for(var e=0,r=3*this.width+this.extraBytes,i=0;i<this.height;i++){for(var o=0;o<this.width;o++){var a=this.pos+i*r+3*o;e++,t[a]=this.buffer[e++],t[a+1]=this.buffer[e++],t[a+2]=this.buffer[e++]}if(this.extraBytes>0){var s=this.pos+i*r+3*this.width;t.fill(0,s,s+this.extraBytes)}}return t},t.exports=function(t,e){return void 0===e&&(e=100),{data:new i(t).encode(),width:t.width,height:t.height}}},443:t=>{t.exports=function(t){if("string"!=typeof t)return!1;var i=t.match(e);if(!i)return!1;var o=i[1];return!!o&&!(!r.test(o)&&!n.test(o))};var e=/^(?:\w+:)?\/\/(\S+)$/,r=/^localhost[\:?\d]*(?:[^\:?\d]\S*)?$/,n=/^[^\s\.]+\.\S{2,}$/},450:(t,e,r)=>{var n=r(398),i=r(834);t.exports={encode:n,decode:i}},535:t=>{"use strict";t.exports=function(t){for(var e,r="",n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=new Uint8Array(t),o=i.byteLength,a=o%3,s=o-a,u=0;u<s;u+=3)r+=n[(16515072&(e=i[u]<<16|i[u+1]<<8|i[u+2]))>>18]+n[(258048&e)>>12]+n[(4032&e)>>6]+n[63&e];return 1===a?(e=i[s],r+="".concat(n[(252&e)>>2]+n[(3&e)<<4],"==")):2===a&&(e=i[s]<<8|i[s+1],r+="".concat(n[(64512&e)>>10]+n[(1008&e)>>4]+n[(15&e)<<2],"=")),r}},545:(t,e,r)=>{"use strict";function n(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i(n.key),n)}}function i(t){var e=function(t){if("object"!=u(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==u(e)?e:e+""}function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(o=function(){return!!t})()}function a(t){return a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},a(t)}function s(t,e){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},s(t,e)}function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}var h=r(768),f=r(773),c="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.hp=y,e.IS=50;var l=2147483647;function p(t){if(t>l)throw new RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,y.prototype),e}function y(t,e,r){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return b(t)}return d(t,e,r)}function d(t,e,r){if("string"==typeof t)return function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!y.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var r=0|A(t,e),n=p(r),i=n.write(t,e);return i!==r&&(n=n.slice(0,i)),n}(t,e);if(ArrayBuffer.isView(t))return function(t){if(et(t,Uint8Array)){var e=new Uint8Array(t);return w(e.buffer,e.byteOffset,e.byteLength)}return v(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+u(t));if(et(t,ArrayBuffer)||t&&et(t.buffer,ArrayBuffer))return w(t,e,r);if("undefined"!=typeof SharedArrayBuffer&&(et(t,SharedArrayBuffer)||t&&et(t.buffer,SharedArrayBuffer)))return w(t,e,r);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return y.from(n,e,r);var i=function(t){if(y.isBuffer(t)){var e=0|m(t.length),r=p(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||rt(t.length)?p(0):v(t):"Buffer"===t.type&&Array.isArray(t.data)?v(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return y.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+u(t))}function g(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function b(t){return g(t),p(t<0?0:0|m(t))}function v(t){for(var e=t.length<0?0:0|m(t.length),r=p(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function w(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;return n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),Object.setPrototypeOf(n,y.prototype),n}function m(t){if(t>=l)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+l.toString(16)+" bytes");return 0|t}function A(t,e){if(y.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||et(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+u(t));var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return $(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return Z(t).length;default:if(i)return n?-1:$(t).length;e=(""+e).toLowerCase(),i=!0}}function E(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return C(this,e,r);case"utf8":case"utf-8":return j(this,e,r);case"ascii":return T(this,e,r);case"latin1":case"binary":return R(this,e,r);case"base64":return _(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function k(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function x(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),rt(r=+r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=y.from(e,n)),y.isBuffer(e))return 0===e.length?-1:O(t,e,r,n,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):O(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function O(t,e,r,n,i){var o,a=1,s=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;a=2,s/=2,u/=2,r/=2}function h(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var f=-1;for(o=r;o<s;o++)if(h(t,o)===h(e,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===u)return f*a}else-1!==f&&(o-=o-f),f=-1}else for(r+u>s&&(r=s-u),o=r;o>=0;o--){for(var c=!0,l=0;l<u;l++)if(h(t,o+l)!==h(e,l)){c=!1;break}if(c)return o}return-1}function I(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o,a=e.length;for(n>a/2&&(n=a/2),o=0;o<n;++o){var s=parseInt(e.substr(2*o,2),16);if(rt(s))return o;t[r+o]=s}return o}function U(t,e,r,n){return tt($(e,t.length-r),t,r,n)}function S(t,e,r,n){return tt(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function L(t,e,r,n){return tt(Z(e),t,r,n)}function B(t,e,r,n){return tt(function(t,e){for(var r,n,i,o=[],a=0;a<t.length&&!((e-=2)<0);++a)n=(r=t.charCodeAt(a))>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function _(t,e,r){return 0===e&&r===t.length?h.fromByteArray(t):h.fromByteArray(t.slice(e,r))}function j(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o=t[i],a=null,s=o>239?4:o>223?3:o>191?2:1;if(i+s<=r){var u=void 0,h=void 0,f=void 0,c=void 0;switch(s){case 1:o<128&&(a=o);break;case 2:128==(192&(u=t[i+1]))&&(c=(31&o)<<6|63&u)>127&&(a=c);break;case 3:u=t[i+1],h=t[i+2],128==(192&u)&&128==(192&h)&&(c=(15&o)<<12|(63&u)<<6|63&h)>2047&&(c<55296||c>57343)&&(a=c);break;case 4:u=t[i+1],h=t[i+2],f=t[i+3],128==(192&u)&&128==(192&h)&&128==(192&f)&&(c=(15&o)<<18|(63&u)<<12|(63&h)<<6|63&f)>65535&&c<1114112&&(a=c)}}null===a?(a=65533,s=1):a>65535&&(a-=65536,n.push(a>>>10&1023|55296),a=56320|1023&a),n.push(a),i+=s}return function(t){var e=t.length;if(e<=P)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=P));return r}(n)}y.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),y.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(y.prototype,"parent",{enumerable:!0,get:function(){if(y.isBuffer(this))return this.buffer}}),Object.defineProperty(y.prototype,"offset",{enumerable:!0,get:function(){if(y.isBuffer(this))return this.byteOffset}}),y.poolSize=8192,y.from=function(t,e,r){return d(t,e,r)},Object.setPrototypeOf(y.prototype,Uint8Array.prototype),Object.setPrototypeOf(y,Uint8Array),y.alloc=function(t,e,r){return function(t,e,r){return g(t),t<=0?p(t):void 0!==e?"string"==typeof r?p(t).fill(e,r):p(t).fill(e):p(t)}(t,e,r)},y.allocUnsafe=function(t){return b(t)},y.allocUnsafeSlow=function(t){return b(t)},y.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==y.prototype},y.compare=function(t,e){if(et(t,Uint8Array)&&(t=y.from(t,t.offset,t.byteLength)),et(e,Uint8Array)&&(e=y.from(e,e.offset,e.byteLength)),!y.isBuffer(t)||!y.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},y.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},y.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return y.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=y.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(et(o,Uint8Array))i+o.length>n.length?(y.isBuffer(o)||(o=y.from(o)),o.copy(n,i)):Uint8Array.prototype.set.call(n,o,i);else{if(!y.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i)}i+=o.length}return n},y.byteLength=A,y.prototype._isBuffer=!0,y.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)k(this,e,e+1);return this},y.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)k(this,e,e+3),k(this,e+1,e+2);return this},y.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)k(this,e,e+7),k(this,e+1,e+6),k(this,e+2,e+5),k(this,e+3,e+4);return this},y.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?j(this,0,t):E.apply(this,arguments)},y.prototype.toLocaleString=y.prototype.toString,y.prototype.equals=function(t){if(!y.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===y.compare(this,t)},y.prototype.inspect=function(){var t="",r=e.IS;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},c&&(y.prototype[c]=y.prototype.inspect),y.prototype.compare=function(t,e,r,n,i){if(et(t,Uint8Array)&&(t=y.from(t,t.offset,t.byteLength)),!y.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+u(t));if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(e>>>=0),s=Math.min(o,a),h=this.slice(n,i),f=t.slice(e,r),c=0;c<s;++c)if(h[c]!==f[c]){o=h[c],a=f[c];break}return o<a?-1:a<o?1:0},y.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},y.prototype.indexOf=function(t,e,r){return x(this,t,e,r,!0)},y.prototype.lastIndexOf=function(t,e,r){return x(this,t,e,r,!1)},y.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return I(this,t,e,r);case"utf8":case"utf-8":return U(this,t,e,r);case"ascii":case"latin1":case"binary":return S(this,t,e,r);case"base64":return L(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},y.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var P=4096;function T(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function R(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function C(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=nt[t[o]];return i}function M(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function N(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function F(t,e,r,n,i,o){if(!y.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function G(t,e,r,n,i){Q(e,n,i,t,r,7);var o=Number(e&BigInt(4294967295));t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o;var a=Number(e>>BigInt(32)&BigInt(4294967295));return t[r++]=a,a>>=8,t[r++]=a,a>>=8,t[r++]=a,a>>=8,t[r++]=a,r}function z(t,e,r,n,i){Q(e,n,i,t,r,7);var o=Number(e&BigInt(4294967295));t[r+7]=o,o>>=8,t[r+6]=o,o>>=8,t[r+5]=o,o>>=8,t[r+4]=o;var a=Number(e>>BigInt(32)&BigInt(4294967295));return t[r+3]=a,a>>=8,t[r+2]=a,a>>=8,t[r+1]=a,a>>=8,t[r]=a,r+8}function D(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function W(t,e,r,n,i){return e=+e,r>>>=0,i||D(t,0,r,4),f.write(t,e,r,n,23,4),r+4}function Y(t,e,r,n,i){return e=+e,r>>>=0,i||D(t,0,r,8),f.write(t,e,r,n,52,8),r+8}y.prototype.slice=function(t,e){var r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,y.prototype),n},y.prototype.readUintLE=y.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||N(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},y.prototype.readUintBE=y.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||N(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},y.prototype.readUint8=y.prototype.readUInt8=function(t,e){return t>>>=0,e||N(t,1,this.length),this[t]},y.prototype.readUint16LE=y.prototype.readUInt16LE=function(t,e){return t>>>=0,e||N(t,2,this.length),this[t]|this[t+1]<<8},y.prototype.readUint16BE=y.prototype.readUInt16BE=function(t,e){return t>>>=0,e||N(t,2,this.length),this[t]<<8|this[t+1]},y.prototype.readUint32LE=y.prototype.readUInt32LE=function(t,e){return t>>>=0,e||N(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},y.prototype.readUint32BE=y.prototype.readUInt32BE=function(t,e){return t>>>=0,e||N(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},y.prototype.readBigUInt64LE=it((function(t){X(t>>>=0,"offset");var e=this[t],r=this[t+7];void 0!==e&&void 0!==r||H(t,this.length-8);var n=e+this[++t]*Math.pow(2,8)+this[++t]*Math.pow(2,16)+this[++t]*Math.pow(2,24),i=this[++t]+this[++t]*Math.pow(2,8)+this[++t]*Math.pow(2,16)+r*Math.pow(2,24);return BigInt(n)+(BigInt(i)<<BigInt(32))})),y.prototype.readBigUInt64BE=it((function(t){X(t>>>=0,"offset");var e=this[t],r=this[t+7];void 0!==e&&void 0!==r||H(t,this.length-8);var n=e*Math.pow(2,24)+this[++t]*Math.pow(2,16)+this[++t]*Math.pow(2,8)+this[++t],i=this[++t]*Math.pow(2,24)+this[++t]*Math.pow(2,16)+this[++t]*Math.pow(2,8)+r;return(BigInt(n)<<BigInt(32))+BigInt(i)})),y.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||N(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},y.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||N(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},y.prototype.readInt8=function(t,e){return t>>>=0,e||N(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},y.prototype.readInt16LE=function(t,e){t>>>=0,e||N(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},y.prototype.readInt16BE=function(t,e){t>>>=0,e||N(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},y.prototype.readInt32LE=function(t,e){return t>>>=0,e||N(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},y.prototype.readInt32BE=function(t,e){return t>>>=0,e||N(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},y.prototype.readBigInt64LE=it((function(t){X(t>>>=0,"offset");var e=this[t],r=this[t+7];void 0!==e&&void 0!==r||H(t,this.length-8);var n=this[t+4]+this[t+5]*Math.pow(2,8)+this[t+6]*Math.pow(2,16)+(r<<24);return(BigInt(n)<<BigInt(32))+BigInt(e+this[++t]*Math.pow(2,8)+this[++t]*Math.pow(2,16)+this[++t]*Math.pow(2,24))})),y.prototype.readBigInt64BE=it((function(t){X(t>>>=0,"offset");var e=this[t],r=this[t+7];void 0!==e&&void 0!==r||H(t,this.length-8);var n=(e<<24)+this[++t]*Math.pow(2,16)+this[++t]*Math.pow(2,8)+this[++t];return(BigInt(n)<<BigInt(32))+BigInt(this[++t]*Math.pow(2,24)+this[++t]*Math.pow(2,16)+this[++t]*Math.pow(2,8)+r)})),y.prototype.readFloatLE=function(t,e){return t>>>=0,e||N(t,4,this.length),f.read(this,t,!0,23,4)},y.prototype.readFloatBE=function(t,e){return t>>>=0,e||N(t,4,this.length),f.read(this,t,!1,23,4)},y.prototype.readDoubleLE=function(t,e){return t>>>=0,e||N(t,8,this.length),f.read(this,t,!0,52,8)},y.prototype.readDoubleBE=function(t,e){return t>>>=0,e||N(t,8,this.length),f.read(this,t,!1,52,8)},y.prototype.writeUintLE=y.prototype.writeUIntLE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||F(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},y.prototype.writeUintBE=y.prototype.writeUIntBE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||F(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},y.prototype.writeUint8=y.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,1,255,0),this[e]=255&t,e+1},y.prototype.writeUint16LE=y.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},y.prototype.writeUint16BE=y.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},y.prototype.writeUint32LE=y.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},y.prototype.writeUint32BE=y.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},y.prototype.writeBigUInt64LE=it((function(t){return G(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,BigInt(0),BigInt("0xffffffffffffffff"))})),y.prototype.writeBigUInt64BE=it((function(t){return z(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,BigInt(0),BigInt("0xffffffffffffffff"))})),y.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);F(this,t,e,r,i-1,-i)}var o=0,a=1,s=0;for(this[e]=255&t;++o<r&&(a*=256);)t<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(t/a|0)-s&255;return e+r},y.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);F(this,t,e,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[e+o]=255&t;--o>=0&&(a*=256);)t<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(t/a|0)-s&255;return e+r},y.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},y.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},y.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},y.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},y.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||F(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},y.prototype.writeBigInt64LE=it((function(t){return G(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),y.prototype.writeBigInt64BE=it((function(t){return z(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),y.prototype.writeFloatLE=function(t,e,r){return W(this,t,e,!0,r)},y.prototype.writeFloatBE=function(t,e,r){return W(this,t,e,!1,r)},y.prototype.writeDoubleLE=function(t,e,r){return Y(this,t,e,!0,r)},y.prototype.writeDoubleBE=function(t,e,r){return Y(this,t,e,!1,r)},y.prototype.copy=function(t,e,r,n){if(!y.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),i},y.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!y.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){var i=t.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(t=i)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var a=y.isBuffer(t)?t:y.from(t,n),s=a.length;if(0===s)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<r-e;++o)this[o+e]=a[o%s]}return this};var V={};function q(t,e,r){V[t]=function(r){function i(){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),r=function(t,e,r){return e=a(e),function(t,e){if(e&&("object"==u(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,o()?Reflect.construct(e,r||[],a(t).constructor):e.apply(t,r))}(this,i),Object.defineProperty(r,"message",{value:e.apply(r,arguments),writable:!0,configurable:!0}),r.name="".concat(r.name," [").concat(t,"]"),r.stack,delete r.name,r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&s(t,e)}(i,r),h=i,(f=[{key:"code",get:function(){return t},set:function(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}},{key:"toString",value:function(){return"".concat(this.name," [").concat(t,"]: ").concat(this.message)}}])&&n(h.prototype,f),Object.defineProperty(h,"prototype",{writable:!1}),h;var h,f}(r)}function J(t){for(var e="",r=t.length,n="-"===t[0]?1:0;r>=n+4;r-=3)e="_".concat(t.slice(r-3,r)).concat(e);return"".concat(t.slice(0,r)).concat(e)}function Q(t,e,r,n,i,o){if(t>r||t<e){var a,s="bigint"==typeof e?"n":"";throw a=o>3?0===e||e===BigInt(0)?">= 0".concat(s," and < 2").concat(s," ** ").concat(8*(o+1)).concat(s):">= -(2".concat(s," ** ").concat(8*(o+1)-1).concat(s,") and < 2 ** ")+"".concat(8*(o+1)-1).concat(s):">= ".concat(e).concat(s," and <= ").concat(r).concat(s),new V.ERR_OUT_OF_RANGE("value",a,t)}!function(t,e,r){X(e,"offset"),void 0!==t[e]&&void 0!==t[e+r]||H(e,t.length-(r+1))}(n,i,o)}function X(t,e){if("number"!=typeof t)throw new V.ERR_INVALID_ARG_TYPE(e,"number",t)}function H(t,e,r){if(Math.floor(t)!==t)throw X(t,r),new V.ERR_OUT_OF_RANGE(r||"offset","an integer",t);if(e<0)throw new V.ERR_BUFFER_OUT_OF_BOUNDS;throw new V.ERR_OUT_OF_RANGE(r||"offset",">= ".concat(r?1:0," and <= ").concat(e),t)}q("ERR_BUFFER_OUT_OF_BOUNDS",(function(t){return t?"".concat(t," is outside of buffer bounds"):"Attempt to access memory outside buffer bounds"}),RangeError),q("ERR_INVALID_ARG_TYPE",(function(t,e){return'The "'.concat(t,'" argument must be of type number. Received type ').concat(u(e))}),TypeError),q("ERR_OUT_OF_RANGE",(function(t,e,r){var n='The value of "'.concat(t,'" is out of range.'),i=r;return Number.isInteger(r)&&Math.abs(r)>Math.pow(2,32)?i=J(String(r)):"bigint"==typeof r&&(i=String(r),(r>Math.pow(BigInt(2),BigInt(32))||r<-Math.pow(BigInt(2),BigInt(32)))&&(i=J(i)),i+="n"),n+" It must be ".concat(e,". Received ").concat(i)}),RangeError);var K=/[^+/0-9A-Za-z-_]/g;function $(t,e){var r;e=e||1/0;for(var n=t.length,i=null,o=[],a=0;a<n;++a){if((r=t.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(a+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function Z(t){return h.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(K,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function tt(t,e,r,n){var i;for(i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}function et(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function rt(t){return t!=t}var nt=function(){for(var t="0123456789abcdef",e=new Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}();function it(t){return"undefined"==typeof BigInt?ot:t}function ot(){throw new Error("BigInt not supported")}},613:(t,e,r)=>{"use strict";function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function i(t){return new Promise((function(e,r){t.oncomplete=t.onsuccess=function(){return e(t.result)},t.onabort=t.onerror=function(){return r(t.error)}}))}function o(t,e){var r=indexedDB.open(t);r.onupgradeneeded=function(){return r.result.createObjectStore(e)};var n=i(r);return function(t,r){return n.then((function(n){return r(n.transaction(e,t).objectStore(e))}))}}var a;function s(){return a||(a=o("keyval-store","keyval")),a}function u(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:s())("readonly",(function(e){return i(e.get(t))}))}function h(t,e){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:s())("readwrite",(function(r){return r.put(e,t),i(r.transaction)}))}function f(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:s())("readwrite",(function(e){return t.forEach((function(t){return e.put(t[1],t[0])})),i(e.transaction)}))}function c(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:s())("readonly",(function(e){return Promise.all(t.map((function(t){return i(e.get(t))})))}))}function l(t,e){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:s())("readwrite",(function(r){return new Promise((function(n,o){r.get(t).onsuccess=function(){try{r.put(e(this.result),t),n(i(r.transaction))}catch(t){o(t)}}}))}))}function p(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:s())("readwrite",(function(e){return e.delete(t),i(e.transaction)}))}function y(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:s())("readwrite",(function(e){return t.forEach((function(t){return e.delete(t)})),i(e.transaction)}))}function d(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:s())("readwrite",(function(t){return t.clear(),i(t.transaction)}))}function g(t,e){return t.openCursor().onsuccess=function(){this.result&&(e(this.result),this.result.continue())},i(t.transaction)}function b(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:s())("readonly",(function(t){if(t.getAllKeys)return i(t.getAllKeys());var e=[];return g(t,(function(t){return e.push(t.key)})).then((function(){return e}))}))}function v(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:s())("readonly",(function(t){if(t.getAll)return i(t.getAll());var e=[];return g(t,(function(t){return e.push(t.value)})).then((function(){return e}))}))}function w(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s();return t("readonly",(function(e){if(e.getAll&&e.getAllKeys)return Promise.all([i(e.getAllKeys()),i(e.getAll())]).then((function(t){var e,r,i=(r=2,function(t){if(Array.isArray(t))return t}(e=t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,s=[],u=!0,h=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);u=!0);}catch(t){h=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(e,r)||function(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=i[0],a=i[1];return o.map((function(t,e){return[t,a[e]]}))}));var r=[];return t("readonly",(function(t){return g(t,(function(t){return r.push([t.key,t.value])})).then((function(){return r}))}))}))}r.r(e),r.d(e,{clear:()=>d,createStore:()=>o,del:()=>p,delMany:()=>y,entries:()=>w,get:()=>u,getMany:()=>c,keys:()=>b,promisifyRequest:()=>i,set:()=>h,setMany:()=>f,update:()=>l,values:()=>v})},670:t=>{"use strict";t.exports={text:!0,blocks:!1,layoutBlocks:!1,hocr:!1,tsv:!1,box:!1,unlv:!1,osd:!1,pdf:!1,imageColor:!1,imageGrey:!1,imageBinary:!1,debug:!1}},768:(t,e)=>{"use strict";e.byteLength=function(t){var e=s(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){var e,r,o=s(t),a=o[0],u=o[1],h=new i(function(t,e,r){return 3*(e+r)/4-r}(0,a,u)),f=0,c=u>0?a-4:a;for(r=0;r<c;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],h[f++]=e>>16&255,h[f++]=e>>8&255,h[f++]=255&e;return 2===u&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,h[f++]=255&e),1===u&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,h[f++]=e>>8&255,h[f++]=255&e),h},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],a=16383,s=0,h=n-i;s<h;s+=a)o.push(u(t,s,s+a>h?h:s+a));return 1===i?(e=t[n-1],o.push(r[e>>2]+r[e<<4&63]+"==")):2===i&&(e=(t[n-2]<<8)+t[n-1],o.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"=")),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0;a<64;++a)r[a]=o[a],n[o.charCodeAt(a)]=a;function s(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function u(t,e,n){for(var i,o,a=[],s=e;s<n;s+=3)i=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),a.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return a.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},773:(t,e)=>{e.read=function(t,e,r,n,i){var o,a,s=8*i-n-1,u=(1<<s)-1,h=u>>1,f=-7,c=r?i-1:0,l=r?-1:1,p=t[e+c];for(c+=l,o=p&(1<<-f)-1,p>>=-f,f+=s;f>0;o=256*o+t[e+c],c+=l,f-=8);for(a=o&(1<<-f)-1,o>>=-f,f+=n;f>0;a=256*a+t[e+c],c+=l,f-=8);if(0===o)o=1-h;else{if(o===u)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,n),o-=h}return(p?-1:1)*a*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var a,s,u,h=8*o-i-1,f=(1<<h)-1,c=f>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,y=n?1:-1,d=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,a=f):(a=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-a))<1&&(a--,u*=2),(e+=a+c>=1?l/u:l*Math.pow(2,1-c))*u>=2&&(a++,u/=2),a+c>=f?(s=0,a=f):a+c>=1?(s=(e*u-1)*Math.pow(2,i),a+=c):(s=e*Math.pow(2,c-1)*Math.pow(2,i),a=0));i>=8;t[r+p]=255&s,p+=y,s/=256,i-=8);for(a=a<<i|s,h+=i;h>0;t[r+p]=255&a,p+=y,a/=256,h-=8);t[r+p-y]|=128*d}},797:(t,e,r)=>{"use strict";var n=r(613),i=n.set,o=n.get,a=n.del;t.exports={readCache:o,writeCache:i,deleteCache:a,checkCache:function(t){return o(t).then((function(t){return void 0!==t}))}}},834:(t,e,r)=>{var n=r(545).hp;function i(t,e){if(this.pos=0,this.buffer=t,this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=this.buffer.toString("utf-8",0,this.pos+=2),"BM"!=this.flag)throw new Error("Invalid BMP File");this.parseHeader(),this.parseRGBA()}i.prototype.parseHeader=function(){if(this.fileSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.reserved=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.offset=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.headerSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.width=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.height=this.buffer.readInt32LE(this.pos),this.pos+=4,this.planes=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.bitPP=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.compress=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.rawSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.hr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.vr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.colors=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.importantColors=this.buffer.readUInt32LE(this.pos),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var r=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);this.palette[e]={red:i,green:n,blue:r,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},i.prototype.parseRGBA=function(){var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new n(e),this[t]()},i.prototype.bit1=function(){var t=Math.ceil(this.width/8),e=t%4,r=this.height>=0?this.height-1:-this.height;for(r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<t;i++)for(var o=this.buffer.readUInt8(this.pos++),a=n*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var u=this.palette[o>>7-s&1];this.data[a+4*s]=0,this.data[a+4*s+1]=u.blue,this.data[a+4*s+2]=u.green,this.data[a+4*s+3]=u.red}0!=e&&(this.pos+=4-e)}},i.prototype.bit4=function(){if(2==this.compress){var t=function(t){var r=this.palette[t];this.data[e]=0,this.data[e+1]=r.blue,this.data[e+2]=r.green,this.data[e+3]=r.red,e+=4};this.data.fill(255);for(var e=0,r=this.bottom_up?this.height-1:0,n=!1;e<this.data.length;){var i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);if(0==i){if(0==o){this.bottom_up?r--:r++,e=r*this.width*4,n=!1;continue}if(1==o)break;if(2==o){var a=this.buffer.readUInt8(this.pos++),s=this.buffer.readUInt8(this.pos++);this.bottom_up?r-=s:r+=s,e+=s*this.width*4+4*a}else{for(var u=this.buffer.readUInt8(this.pos++),h=0;h<o;h++)t.call(this,n?15&u:(240&u)>>4),1&h&&h+1<o&&(u=this.buffer.readUInt8(this.pos++)),n=!n;1==(o+1>>1&1)&&this.pos++}}else for(h=0;h<i;h++)t.call(this,n?15&o:(240&o)>>4),n=!n}}else{var f=Math.ceil(this.width/2),c=f%4;for(s=this.height-1;s>=0;s--){var l=this.bottom_up?s:this.height-1-s;for(a=0;a<f;a++){o=this.buffer.readUInt8(this.pos++),e=l*this.width*4+2*a*4;var p=o>>4,y=15&o,d=this.palette[p];if(this.data[e]=0,this.data[e+1]=d.blue,this.data[e+2]=d.green,this.data[e+3]=d.red,2*a+1>=this.width)break;d=this.palette[y],this.data[e+4]=0,this.data[e+4+1]=d.blue,this.data[e+4+2]=d.green,this.data[e+4+3]=d.red}0!=c&&(this.pos+=4-c)}}},i.prototype.bit8=function(){if(1==this.compress){var t=function(t){var r=this.palette[t];this.data[e]=0,this.data[e+1]=r.blue,this.data[e+2]=r.green,this.data[e+3]=r.red,e+=4};this.data.fill(255);for(var e=0,r=this.bottom_up?this.height-1:0;e<this.data.length;){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++);if(0==n){if(0==i){this.bottom_up?r--:r++,e=r*this.width*4;continue}if(1==i)break;if(2==i){var o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++);this.bottom_up?r-=a:r+=a,e+=a*this.width*4+4*o}else{for(var s=0;s<i;s++){var u=this.buffer.readUInt8(this.pos++);t.call(this,u)}!0&i&&this.pos++}}else for(s=0;s<n;s++)t.call(this,i)}}else{var h=this.width%4;for(a=this.height-1;a>=0;a--){var f=this.bottom_up?a:this.height-1-a;for(o=0;o<this.width;o++)if(i=this.buffer.readUInt8(this.pos++),e=f*this.width*4+4*o,i<this.palette.length){var c=this.palette[i];this.data[e]=0,this.data[e+1]=c.blue,this.data[e+2]=c.green,this.data[e+3]=c.red}else this.data[e]=0,this.data[e+1]=255,this.data[e+2]=255,this.data[e+3]=255;0!=h&&(this.pos+=4-h)}}},i.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<this.width;i++){var o=this.buffer.readUInt16LE(this.pos);this.pos+=2;var a=(o&e)/e*255|0,s=(o>>5&e)/e*255|0,u=(o>>10&e)/e*255|0,h=o>>15?255:0,f=n*this.width*4+4*i;this.data[f]=h,this.data[f+1]=a,this.data[f+2]=s,this.data[f+3]=u}this.pos+=t}},i.prototype.bit16=function(){var t=this.width%2*2;this.maskRed=31744,this.maskGreen=992,this.maskBlue=31,this.mask0=0,3==this.compress&&(this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4);for(var e=[0,0,0],r=0;r<16;r++)this.maskRed>>r&1&&e[0]++,this.maskGreen>>r&1&&e[1]++,this.maskBlue>>r&1&&e[2]++;e[1]+=e[0],e[2]+=e[1],e[0]=8-e[0],e[1]-=8,e[2]-=8;for(var n=this.height-1;n>=0;n--){for(var i=this.bottom_up?n:this.height-1-n,o=0;o<this.width;o++){var a=this.buffer.readUInt16LE(this.pos);this.pos+=2;var s=(a&this.maskBlue)<<e[0],u=(a&this.maskGreen)>>e[1],h=(a&this.maskRed)>>e[2],f=i*this.width*4+4*o;this.data[f]=0,this.data[f+1]=s,this.data[f+2]=u,this.data[f+3]=h}this.pos+=t}},i.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=e*this.width*4+4*r;this.data[a]=0,this.data[a+1]=n,this.data[a+2]=i,this.data[a+3]=o}this.pos+=this.width%4}},i.prototype.bit32=function(){if(3==this.compress){this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4;for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r;this.data[s]=n,this.data[s+1]=i,this.data[s+2]=o,this.data[s+3]=a}}else for(t=this.height-1;t>=0;t--)for(e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++)i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r,this.data[s]=n,this.data[s+1]=i,this.data[s+2]=o,this.data[s+3]=a},i.prototype.getData=function(){return this.data},t.exports=function(t){return new i(t)}},938:t=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}t.exports=function(t){var r={};return"undefined"!=typeof WorkerGlobalScope?r.type="webworker":"object"===("undefined"==typeof document?"undefined":e(document))?r.type="browser":"object"===("undefined"==typeof process?"undefined":e(process))&&(r.type="node"),void 0===t?r:r[t]}},968:function(t,e,r){"use strict";var n=this;function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=function(t){var e=function(t){if("object"!=i(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==i(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(){h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",f=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof w?e:w,a=Object.create(i.prototype),s=new j(n||[]);return o(a,"_invoke",{value:S(t,r,s)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var y="suspendedStart",d="suspendedYield",g="executing",b="completed",v={};function w(){}function m(){}function A(){}var E={};c(E,s,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(P([])));x&&x!==r&&n.call(x,s)&&(E=x);var O=A.prototype=w.prototype=Object.create(E);function I(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function U(t,e){function r(o,a,s,u){var h=p(t[o],t,a);if("throw"!==h.type){var f=h.arg,c=f.value;return c&&"object"==i(c)&&n.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,s,u)}),(function(t){r("throw",t,s,u)})):e.resolve(c).then((function(t){f.value=t,s(f)}),(function(t){return r("throw",t,s,u)}))}u(h.arg)}var a;o(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function S(e,r,n){var i=y;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===b){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=L(s,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===y)throw i=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var h=p(e,r,n);if("normal"===h.type){if(i=n.done?b:d,h.arg===v)continue;return{value:h.arg,done:n.done}}"throw"===h.type&&(i=b,n.method="throw",n.arg=h.arg)}}}function L(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,L(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=p(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function B(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(B,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return m.prototype=A,o(O,"constructor",{value:A,configurable:!0}),o(A,"constructor",{value:m,configurable:!0}),m.displayName=c(A,f,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,A):(t.__proto__=A,c(t,f,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},I(U.prototype),c(U.prototype,u,(function(){return this})),e.AsyncIterator=U,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new U(l(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},I(O),c(O,f,"Generator"),c(O,s,(function(){return this})),c(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(_),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),h=n.call(a,"finallyLoc");if(u&&h){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!h)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;_(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function f(t,e,r,n,i,o,a){try{var s=t[o](a),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,i)}function c(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){f(o,n,i,a,s,"next",t)}function s(t){f(o,n,i,a,s,"throw",t)}a(void 0)}))}}r(30);var l,p,y,d,g=r(443),b=r(334),v=r(938)("type"),w=r(329),m=r(670),A=r(65),E=A.log,k=A.setLogging,x=r(971),O=null,I={},U={},S=!1,L=function(){var t=c(h().mark((function t(e,r){var n,i,o,a,s,u,f,c;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.workerId,i=e.jobId,o=e.payload.options,a=o.lstmOnly,s=o.corePath,u=o.logging,k(u),f="initializing tesseract",l){t.next=11;break}return t.next=6,I.getCore(a,s,r);case 6:c=t.sent,r.progress({workerId:n,status:f,progress:0}),c({TesseractProgress:function(t){p.progress({workerId:n,jobId:i,status:"recognizing text",progress:Math.max(0,(t-30)/70)})}}).then((function(t){l=t,r.progress({workerId:n,status:f,progress:1}),r.resolve({loaded:!0})})),t.next=12;break;case 11:r.resolve({loaded:!0});case 12:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}(),B=function(){var t=c(h().mark((function t(e,r){var n,i,o,a,s;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=e.workerId,o=e.payload,a=o.method,s=o.args,E("[".concat(i,"]: FS.").concat(a)),r.resolve((n=l.FS)[a].apply(n,function(t){if(Array.isArray(t))return u(t)}(h=s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(h)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(h)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()));case 3:case"end":return t.stop()}var h}),t)})));return function(e,r){return t.apply(this,arguments)}}(),_=function(){var t=c(h().mark((function t(e,r){var n,i,o,a,s,u,f,p,b,w,m,A,k,x,O;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.workerId,i=e.payload,o=i.langs,a=i.options,s=a.langPath,u=a.dataPath,f=a.cachePath,p=a.cacheMethod,b=a.gzip,w=void 0===b||b,m=a.lstmOnly,y=o,d={langPath:s,dataPath:u,cachePath:f,cacheMethod:p,gzip:w,lstmOnly:m},A="loading language traineddata",k="string"==typeof o?o.split("+"):o,x=0,O=function(){var t=c(h().mark((function t(e){var i,o,a,c,y,d,b,O,U;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i="string"==typeof e?e:e.code,o=["refresh","none"].includes(p)?function(){return Promise.resolve()}:I.readCache,a=null,c=!1,t.prev=4,t.next=7,o("".concat(f||".","/").concat(i,".traineddata"));case 7:if(void 0===(y=t.sent)){t.next=14;break}E("[".concat(n,"]: Load ").concat(i,".traineddata from cache")),a=y,S=!0,t.next=15;break;case 14:throw Error("Not found in cache");case 15:t.next=45;break;case 17:if(t.prev=17,t.t0=t.catch(4),c=!0,E("[".concat(n,"]: Load ").concat(i,".traineddata from ").concat(s)),"string"!=typeof e){t.next=44;break}if(d=null,b=s||"https://cdn.jsdelivr.net/npm/@tesseract.js-data/".concat(i,m?"/4.0.0_best_int":"/4.0.0"),("node"!==v||g(b)||b.startsWith("moz-extension://")||b.startsWith("chrome-extension://")||b.startsWith("file://"))&&(d=b.replace(/\/$/,"")),null===d){t.next=39;break}return O="".concat(d,"/").concat(i,".traineddata").concat(w?".gz":""),t.next=29,("webworker"===v?fetch:I.fetch)(O);case 29:if((U=t.sent).ok){t.next=32;break}throw Error("Network error while fetching ".concat(O,". Response code: ").concat(U.status));case 32:return t.t1=Uint8Array,t.next=35,U.arrayBuffer();case 35:t.t2=t.sent,a=new t.t1(t.t2),t.next=42;break;case 39:return t.next=41,I.readCache("".concat(b,"/").concat(i,".traineddata").concat(w?".gz":""));case 41:a=t.sent;case 42:t.next=45;break;case 44:a=e.data;case 45:if(x+=.5/k.length,r&&r.progress({workerId:n,status:A,progress:x}),(31===a[0]&&139===a[1]||31===a[1]&&139===a[0])&&(a=I.gunzip(a)),l){if(u)try{l.FS.mkdir(u)}catch(t){r&&r.reject(t.toString())}l.FS.writeFile("".concat(u||".","/").concat(i,".traineddata"),a)}if(!c||!["write","refresh",void 0].includes(p)){t.next=60;break}return t.prev=51,t.next=54,I.writeCache("".concat(f||".","/").concat(i,".traineddata"),a);case 54:t.next=60;break;case 56:t.prev=56,t.t3=t.catch(51),E("[".concat(n,"]: Failed to write ").concat(i,".traineddata to cache due to error:")),E(t.t3.toString());case 60:x+=.5/k.length,100===Math.round(100*x)&&(x=1),r&&r.progress({workerId:n,status:A,progress:x});case 63:case"end":return t.stop()}}),t,null,[[4,17],[51,56]])})));return function(e){return t.apply(this,arguments)}}(),r&&r.progress({workerId:n,status:A,progress:0}),t.prev=8,t.next=11,Promise.all(k.map(O));case 11:r&&r.resolve(o),t.next=17;break;case 14:t.prev=14,t.t0=t.catch(8),r&&r.reject(t.t0.toString());case 17:case"end":return t.stop()}}),t,null,[[8,14]])})));return function(e,r){return t.apply(this,arguments)}}(),j=function(){var t=c(h().mark((function t(e,r){var n,i,o;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=e.payload.params,i=["ambigs_debug_level","user_words_suffix","user_patterns_suffix","user_patterns_suffix","load_system_dawg","load_freq_dawg","load_unambig_dawg","load_punc_dawg","load_number_dawg","load_bigram_dawg","tessedit_ocr_engine_mode","tessedit_init_config_only","language_model_ngram_on","language_model_use_sigmoidal_certainty"],(o=Object.keys(n).filter((function(t){return i.includes(t)})).join(", ")).length>0&&console.log("Attempted to set parameters that can only be set during initialization: ".concat(o)),Object.keys(n).filter((function(t){return!t.startsWith("tessjs_")})).forEach((function(t){O.SetVariable(t,n[t])})),U=a(a({},U),n),void 0!==r&&r.resolve(U);case 7:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}(),P=function(){var t=c(h().mark((function t(e,r){var n,o,a,s,u,f,c,p,g,b,v,w,m,A;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.workerId,o=e.payload,a=o.langs,s=o.oem,u=o.config,f="string"==typeof a?a:a.map((function(t){return"string"==typeof t?t:t.data})).join("+"),c="initializing api",t.prev=3,r.progress({workerId:n,status:c,progress:0}),null!==O&&O.End(),u&&"object"===i(u)&&Object.keys(u).length>0?g=JSON.stringify(u).replace(/,/g,"\n").replace(/:/g," ").replace(/["'{}]/g,""):u&&"string"==typeof u&&(g=u),"string"==typeof g&&(p="/config",l.FS.writeFile(p,g)),O=new l.TessBaseAPI,-1!==(b=O.Init(null,f,s,p))){t.next=30;break}if(!["write","refresh",void 0].includes(d.cacheMethod)){t.next=30;break}return v=f.split("+"),w=v.map((function(t){return I.deleteCache("".concat(d.cachePath||".","/").concat(t,".traineddata"))})),t.next=16,Promise.all(w);case 16:if(m=l.FS.readFile("/debugDev.txt",{encoding:"utf8",flags:"a+"}),!S||!/components are not present/.test(m)){t.next=30;break}return E("Data from cache missing requested OEM model. Attempting to refresh cache with new language data."),t.next=21,_({workerId:n,payload:{langs:y,options:d}});case 21:if(-1!==(b=O.Init(null,f,s,p))){t.next=29;break}return E("Language data refresh failed."),A=v.map((function(t){return I.deleteCache("".concat(d.cachePath||".","/").concat(t,".traineddata"))})),t.next=27,Promise.all(A);case 27:t.next=30;break;case 29:E("Language data refresh successful.");case 30:-1===b&&r.reject("initialization failed"),r.progress({workerId:n,status:c,progress:1}),r.resolve(),t.next=38;break;case 35:t.prev=35,t.t0=t.catch(3),r.reject(t.t0.toString());case 38:case"end":return t.stop()}}),t,null,[[3,35]])})));return function(e,r){return t.apply(this,arguments)}}(),T=function(t){for(var e=JSON.parse(JSON.stringify(m)),r=["imageColor","imageGrey","imageBinary","layoutBlocks","debug"],n=0,i=0,o=Object.keys(t);i<o.length;i++){var a=o[i];e[a]=t[a]}for(var s=0,u=Object.keys(e);s<u.length;s++){var h=u[s];e[h]&&(r.includes(h)||(n+=1))}return{workingOutput:e,skipRecognition:0===n}},R=["rectangle","pdfTitle","pdfTextOnly","rotateAuto","rotateRadians"],C=function(){var t=c(h().mark((function t(e,r){var n,o,a,s,u,f,c,p,y,d,g,v,m,A,k,I,U,S,L,B,_,j;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=e.payload,o=n.image,a=n.options,s=n.output;try{if(u={},"object"===i(a)&&Object.keys(a).length>0)for(f=0,c=Object.keys(a);f<c.length;f++)(p=c[f]).startsWith("tessjs_")||R.includes(p)||(u[p]=a[p]);if(s.debug&&(u.debug_file="/debugInternal.txt",l.FS.writeFile("/debugInternal.txt","")),Object.keys(u).length>0)for(O.SaveParameters(),y=0,d=Object.keys(u);y<d.length;y++)g=d[y],O.SetVariable(g,u[g]);v=T(s),m=v.workingOutput,A=v.skipRecognition,a.rotateAuto?(I=O.GetPageSegMode(),U=!1,[x.AUTO,x.AUTO_ONLY,x.OSD].includes(String(I))||(U=!0,O.SetVariable("tessedit_pageseg_mode",String(x.AUTO))),w(l,O,o),O.FindLines(),S=O.GetGradient?O.GetGradient():O.GetAngle(),U&&O.SetVariable("tessedit_pageseg_mode",String(I)),Math.abs(S)>=.005?w(l,O,o,k=S):(U&&w(l,O,o),k=0)):(k=a.rotateRadians||0,w(l,O,o,k)),"object"===i(L=a.rectangle)&&O.SetRectangle(L.left,L.top,L.width,L.height),A?(s.layoutBlocks&&O.AnalyseLayout(),E("Skipping recognition: all output options requiring recognition are disabled.")):O.Recognize(null),B=a.pdfTitle,_=a.pdfTextOnly,(j=b(l,O,m,{pdfTitle:B,pdfTextOnly:_,skipRecognition:A})).rotateRadians=k,s.debug&&l.FS.unlink("/debugInternal.txt"),Object.keys(u).length>0&&O.RestoreParameters(),r.resolve(j)}catch(t){r.reject(t.toString())}case 2:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}(),M=function(){var t=c(h().mark((function t(e,r){var n,i,o,a,s;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=e.payload.image;try{w(l,O,n),i=new l.OSResults,O.DetectOS(i)?(o=i.best_result,a=o.orientation_id,s=o.script_id,r.resolve({tesseract_script_id:s,script:i.unicharset.get_script_from_script_id(s),script_confidence:o.sconfidence,orientation_degrees:[0,270,180,90][a],orientation_confidence:o.oconfidence})):r.resolve({tesseract_script_id:null,script:null,script_confidence:null,orientation_degrees:null,orientation_confidence:null})}catch(t){r.reject(t.toString())}case 2:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}(),N=function(){var t=c(h().mark((function t(e,r){return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:try{null!==O&&O.End(),r.resolve({terminated:!0})}catch(t){r.reject(t.toString())}case 1:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}();e.dispatchHandlers=function(t,e){var r=function(r,n){var i={jobId:t.jobId,workerId:t.workerId,action:t.action};e(a(a({},i),{},{status:r,data:n}))};r.resolve=r.bind(n,"resolve"),r.reject=r.bind(n,"reject"),r.progress=r.bind(n,"progress"),p=r,{load:L,FS:B,loadLanguage:_,initialize:P,setParameters:j,recognize:C,detect:M,terminate:N}[t.action](t,r).catch((function(t){return r.reject(t.toString())}))},e.setAdapter=function(t){I=t}},971:t=>{"use strict";t.exports={OSD_ONLY:"0",AUTO_OSD:"1",AUTO_ONLY:"2",AUTO:"3",SINGLE_COLUMN:"4",SINGLE_BLOCK_VERT_TEXT:"5",SINGLE_BLOCK:"6",SINGLE_LINE:"7",SINGLE_WORD:"8",CIRCLE_WORD:"9",SINGLE_CHAR:"10",SPARSE_TEXT:"11",SPARSE_TEXT_OSD:"12",RAW_LINE:"13"}},976:(t,e,r)=>{"use strict";function n(){n=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",h=s.asyncIterator||"@@asyncIterator",f=s.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof w?e:w,o=Object.create(i.prototype),s=new j(n||[]);return a(o,"_invoke",{value:S(t,r,s)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var y="suspendedStart",d="suspendedYield",g="executing",b="completed",v={};function w(){}function m(){}function A(){}var E={};c(E,u,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(P([])));x&&x!==r&&o.call(x,u)&&(E=x);var O=A.prototype=w.prototype=Object.create(E);function I(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function U(t,e){function r(n,a,s,u){var h=p(t[n],t,a);if("throw"!==h.type){var f=h.arg,c=f.value;return c&&"object"==i(c)&&o.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,s,u)}),(function(t){r("throw",t,s,u)})):e.resolve(c).then((function(t){f.value=t,s(f)}),(function(t){return r("throw",t,s,u)}))}u(h.arg)}var n;a(this,"_invoke",{value:function(t,i){function o(){return new e((function(e,n){r(t,i,e,n)}))}return n=n?n.then(o,o):o()}})}function S(e,r,n){var i=y;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===b){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=L(s,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===y)throw i=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var h=p(e,r,n);if("normal"===h.type){if(i=n.done?b:d,h.arg===v)continue;return{value:h.arg,done:n.done}}"throw"===h.type&&(i=b,n.method="throw",n.arg=h.arg)}}}function L(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,L(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=p(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function B(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(B,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[u];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function r(){for(;++n<e.length;)if(o.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return m.prototype=A,a(O,"constructor",{value:A,configurable:!0}),a(A,"constructor",{value:m,configurable:!0}),m.displayName=c(A,f,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,A):(t.__proto__=A,c(t,f,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},I(U.prototype),c(U.prototype,h,(function(){return this})),e.AsyncIterator=U,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new U(l(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},I(O),c(O,f,"Generator"),c(O,u,(function(){return this})),c(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(_),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var u=o.call(a,"catchLoc"),h=o.call(a,"finallyLoc");if(u&&h){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!h)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;_(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e,r,n,i,o,a){try{var s=t[o](a),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,i)}var a=r(242).simd,s=r(330).El.QE;t.exports=function(){var t,e=(t=n().mark((function t(e,o,u){var h,f,c,l;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0!==r.g.TesseractCore){t.next=20;break}if(h="loading tesseract core",u.progress({status:h,progress:0}),"js"!==(f=o||"https://cdn.jsdelivr.net/npm/tesseract.js-core@v".concat(s.substring(1))).slice(-2)){t.next=8;break}c=f,t.next=12;break;case 8:return t.next=10,a();case 10:l=t.sent,c="".concat(f.replace(/\/$/,""),l?e?"/tesseract-core-simd-lstm.wasm.js":"/tesseract-core-simd.wasm.js":e?"/tesseract-core-lstm.wasm.js":"/tesseract-core.wasm.js");case 12:if(r.g.importScripts(c),void 0!==r.g.TesseractCore||void 0===r.g.TesseractCoreWASM||"object"!==("undefined"==typeof WebAssembly?"undefined":i(WebAssembly))){t.next=17;break}r.g.TesseractCore=r.g.TesseractCoreWASM,t.next=19;break;case 17:if(void 0!==r.g.TesseractCore){t.next=19;break}throw Error("Failed to load TesseractCore");case 19:u.progress({status:h,progress:1});case 20:return t.abrupt("return",r.g.TesseractCore);case 21:case"end":return t.stop()}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function s(t){o(a,n,i,s,u,"next",t)}function u(t){o(a,n,i,s,u,"throw",t)}s(void 0)}))});return function(t,r,n){return e.apply(this,arguments)}}()}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={id:n,loaded:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nmd=t=>(t.paths=[],t.children||(t.children=[]),t),(()=>{"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function n(e,r,n){return(r=function(e){var r=function(e){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var i=r(968),o=r(976),a=r(258),s=r(797);r.g.addEventListener("message",(function(t){var e=t.data;i.dispatchHandlers(e,(function(t){return postMessage(t)}))})),i.setAdapter(function(t){for(var r=1;r<arguments.length;r++){var i=null!=arguments[r]?arguments[r]:{};r%2?e(Object(i),!0).forEach((function(e){n(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):e(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}({getCore:o,gunzip:a,fetch:function(){}},s))})()})();
//# sourceMappingURL=worker.min.js.map