<template>
 
    <!-- 应用标题 -->
    <h1 class="title">中文词语手写测试</h1>
    
    <!-- 词语信息显示区域 -->
    <div class="word-info">
      <!-- 显示当前词语的拼音 -->
      <p class="pinyin">拼音: {{ currentWord.pinyin }}</p>
      <!-- 播放发音按钮 -->
      <button @click="playPronunciation" class="play-btn">
        播放发音
      </button>
    </div>
    
    <!-- 字符状态显示区域 - 显示每个字符的识别状态 -->
    <div class="grid-status">
      <div 
        v-for="(char, index) in currentWord.text" 
        :key="index"
        class="char-status"
        :class="getCharStatusClass(index)"
      >
        {{ char }}
        <div style="font-size: 12px; margin-top: 2px;">
          {{ getCharStatusText(index) }}
        </div>
      </div>
    </div>

    <!-- 画布容器 - 包含手写区域 -->
    <div class="canvas-container" :class="{ loading: isRecognizing }">
      <!-- 手写画布 - 用户在此区域手写汉字 -->
      <canvas
        ref="canvas"
        :width="canvasWidth"
        :height="canvasHeight"
        class="writing-canvas"
        @mousedown="startDrawing"
        @mousemove="draw"
        @mouseup="stopDrawing"
        @mouseleave="stopDrawing"
        @touchstart.prevent="startDrawing"
        @touchmove.prevent="draw"
        @touchend.prevent="stopDrawing"
        @touchcancel.prevent="stopDrawing"
      ></canvas>
    </div>
    
    <!-- 主要操作按钮组 -->
    <div class="button-group">
      <!-- 清除画布按钮 -->
      <button
        @click="clearCanvas"
        :disabled="isRecognizing"
        class="btn btn-clear"
      >
        清除画布
      </button>
      
      <!-- 撤销上一笔按钮 -->
      <button
        @click="undoLastStroke"
        :disabled="isRecognizing || strokeHistory.length === 0"
        class="btn btn-undo"
      >
        撤销
      </button>
      
      <!-- 查看提取图像按钮 - 用于调试 -->
      <button
        @click="showDebugImages"
        :disabled="isRecognizing"
        class="btn btn-debug"
      >
        查看提取图像
      </button>
      
      <!-- 提交识别按钮 -->
      <button
        @click="recognizeAllChars"
        :disabled="isRecognizing"
        class="btn btn-submit"
      >
        <div class="btn-content">
          <span v-if="!isRecognizing">提交识别</span>
          <span v-else>
            <div class="spinner"></div>
            识别中...
          </span>
        </div>
      </button>
    </div>
    
    <!-- 下一个词语按钮 - 当所有字符都识别正确时显示 -->
    <div class="button-group" v-if="allCharsCompleted">
      <button
        @click="nextWord"
        class="btn btn-check"
        style="width: 100%;"
      >
        下一个词语
      </button>
    </div>

    <!-- 识别状态显示区域 -->
    <div class="recognition-status">
      <p v-if="feedback" class="feedback" :class="feedback.isCorrect ? 'correct' : 'incorrect'">
        {{ feedback.message }}
      </p>
    </div>
    
    <!-- 识别结果显示 -->
    <p v-if="recognizedText && !isRecognizing" class="recognized-text">
      识别结果: {{ recognizedText }}
    </p>
    
    <!-- 调试面板 - 显示提取的字符图像 -->
    <div v-if="debugImages.length > 0" class="debug-panel">
      <div class="debug-title">提取的字符图像（用于OCR识别）</div>
      <div class="debug-images">
        <div v-for="(debugImg, index) in debugImages" :key="index" class="debug-char">
          <!-- 显示提取的字符图像 -->
          <canvas 
            :ref="`debugCanvas${index}`"
            :width="debugImg.width" 
            :height="debugImg.height"
            style="width: 80px; height: 80px;"
          ></canvas>
          <!-- 显示字符信息和识别结果 -->
          <div class="debug-char-label">
            字符{{ index + 1 }}: {{ currentWord.text[index] }}
            <br>
            <span v-if="charResults[index]" :style="{ color: charResults[index].isCorrect ? '#28a745' : '#dc3545' }">
              识别: {{ charResults[index].recognized || '无' }}
            </span>
          </div>
        </div>
      </div>
    </div> 
</template>

<script>
import 
// 计算属性 - 基于数据自动计算的属性
      computed: {
        // 获取当前词语对象
        currentWord() {
          return this.words[this.currentIndex];
        },

        // 计算画布宽度 - 根据字符数量、格子大小和间距计算
        canvasWidth() {
          return this.currentWord.text.length * this.gridSize + (this.currentWord.text.length - 1) * this.spacing;
        },

        // 计算画布高度 - 等于单个格子的高度
        canvasHeight() {
          return this.gridSize;
        },

        // 检查是否所有字符都识别正确
        allCharsCompleted() {
          return this.charResults.length === this.currentWord.text.length &&
                 this.charResults.every(result => result && result.isCorrect);
        }
      },

      // 组件挂载后的钩子函数
      mounted() {
        // 获取画布的2D绘图上下文
        this.ctx = this.$refs.canvas.getContext('2d');

        // 设置画笔属性
        this.ctx.lineWidth = 3; // 线条宽度
        this.ctx.lineCap = 'round'; // 线条端点样式为圆形
        this.ctx.lineJoin = 'round'; // 线条连接点样式为圆形
        this.ctx.strokeStyle = '#000000'; // 线条颜色为黑色
        this.ctx.fillStyle = '#000000'; // 填充颜色为黑色

        // 设置高DPI支持，确保在高分辨率屏幕上显示清晰
        this.setupHighDPI();

        // 绘制田字格背景
        this.drawTianziGrid();
      },

      // 监听器 - 监听数据变化并执行相应操作
      watch: {
        // 当前词语改变时重置状态
        currentWord() {
          this.resetWordState();
        }
      },

      // 方法定义 - 应用的各种功能函数
      methods: {
        // 设置高DPI支持 - 确保在高分辨率屏幕上显示清晰
        setupHighDPI() {
          const canvas = this.$refs.canvas;
          const dpr = window.devicePixelRatio || 1; // 获取设备像素比

          // 设置画布的实际像素大小
          canvas.width = this.canvasWidth * dpr;
          canvas.height = this.canvasHeight * dpr;

          // 设置画布的CSS显示大小
          canvas.style.width = this.canvasWidth + 'px';
          canvas.style.height = this.canvasHeight + 'px';

          // 缩放绘图上下文以匹配设备像素比
          this.ctx.scale(dpr, dpr);
        },

        // 绘制田字格背景
        drawTianziGrid() {
          // 清除整个画布
          this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

          // 保存当前绘图状态
          this.ctx.save();

          // 为每个字符绘制田字格
          for (let i = 0; i < this.currentWord.text.length; i++) {
            const x = i * (this.gridSize + this.spacing); // 计算当前格子的X坐标

            // 绘制外框 - 使用较粗的线条
            this.ctx.strokeStyle = '#999999';
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(x, 0, this.gridSize, this.gridSize);

            // 绘制内部辅助线 - 使用细线
            this.ctx.strokeStyle = '#dddddd';
            this.ctx.lineWidth = 1;

            // 绘制垂直中线
            this.ctx.beginPath();
            this.ctx.moveTo(x + this.gridSize / 2, 0);
            this.ctx.lineTo(x + this.gridSize / 2, this.gridSize);
            this.ctx.stroke();

            // 绘制水平中线
            this.ctx.beginPath();
            this.ctx.moveTo(x, this.gridSize / 2);
            this.ctx.lineTo(x + this.gridSize, this.gridSize / 2);
            this.ctx.stroke();

            // 绘制对角线 - 使用更淡的颜色
            this.ctx.strokeStyle = '#eeeeee';
            this.ctx.lineWidth = 0.5;

            // 左上到右下对角线
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x + this.gridSize, this.gridSize);
            this.ctx.stroke();

            // 右上到左下对角线
            this.ctx.beginPath();
            this.ctx.moveTo(x + this.gridSize, 0);
            this.ctx.lineTo(x, this.gridSize);
            this.ctx.stroke();
          }

          // 恢复绘图状态，为手写做准备
          this.ctx.restore();
          this.ctx.lineWidth = 3;
          this.ctx.lineCap = 'round';
          this.ctx.lineJoin = 'round';
          this.ctx.strokeStyle = '#000000';
          this.ctx.fillStyle = '#000000';
        },

        // 保存当前画布状态到历史记录（用于撤销功能）
        saveCanvasState() {
          const imageData = this.ctx.getImageData(0, 0, this.$refs.canvas.width, this.$refs.canvas.height);
          this.strokeHistory.push(imageData);

          // 限制历史记录数量，避免内存过多占用
          if (this.strokeHistory.length > 20) {
            this.strokeHistory.shift(); // 删除最早的记录
          }
        },

        // 播放当前词语的发音
        playPronunciation() {
          const audio = new Audio(this.currentWord.audio);
          audio.play().catch(error => {
            console.error('播放发音失败:', error);
          });
        },

        // 获取事件在画布中的坐标位置
        getEventPos(e) {
          const rect = this.$refs.canvas.getBoundingClientRect();
          const clientX = e.clientX || (e.touches && e.touches[0].clientX);
          const clientY = e.clientY || (e.touches && e.touches[0].clientY);
          return {
            x: (clientX - rect.left) * (this.$refs.canvas.width / rect.width) / (window.devicePixelRatio || 1),
            y: (clientY - rect.top) * (this.$refs.canvas.height / rect.height) / (window.devicePixelRatio || 1)
          };
        },

        // 获取坐标所在的格子索引
        getGridIndex(x, y) {
          for (let i = 0; i < this.currentWord.text.length; i++) {
            const gridX = i * (this.gridSize + this.spacing);
            if (x >= gridX && x <= gridX + this.gridSize && y >= 0 && y <= this.gridSize) {
              return i; // 返回格子索引
            }
          }
          return -1; // 不在任何格子内
        },

        // 检查坐标是否在任何格子内
        isInAnyGrid(x, y) {
          return this.getGridIndex(x, y) !== -1;
        },

        // 开始绘制 - 鼠标按下或触摸开始时调用
        startDrawing(e) {
          if (this.isRecognizing) return; // 识别中时禁止绘制

          const pos = this.getEventPos(e);

          // 检查是否在任何格子内
          const gridIndex = this.getGridIndex(pos.x, pos.y);
          if (gridIndex === -1) return; // 不在格子内则不开始绘制

          // 记录当前绘制的格子索引
          this.currentDrawingGrid = gridIndex;

          // 在开始新的笔画前保存当前状态
          this.saveCanvasState();

          this.isDrawing = true;
          this.lastX = pos.x;
          this.lastY = pos.y;
          this.currentStroke = [pos];
          this.ctx.beginPath();
          this.ctx.moveTo(pos.x, pos.y);
        },

        // 绘制过程 - 鼠标移动或触摸移动时调用
        draw(e) {
          if (!this.isDrawing || this.isRecognizing) return;
          const pos = this.getEventPos(e);

          // 检查是否还在同一个格子内
          const gridIndex = this.getGridIndex(pos.x, pos.y);
          if (gridIndex !== this.currentDrawingGrid) {
            this.stopDrawing(); // 离开当前格子则停止绘制
            return;
          }

          // 记录当前笔画的点
          this.currentStroke.push(pos);

          // 使用二次贝塞尔曲线使线条更平滑
          this.ctx.quadraticCurveTo(this.lastX, this.lastY, (pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2);
          this.ctx.stroke();
          this.ctx.beginPath();
          this.ctx.moveTo((pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2);

          this.lastX = pos.x;
          this.lastY = pos.y;
        },

        // 停止绘制 - 鼠标抬起或触摸结束时调用
        stopDrawing() {
          if (this.isDrawing) {
            this.isDrawing = false;
            this.ctx.closePath();
            this.currentStroke = []; // 清空当前笔画
            this.currentDrawingGrid = -1; // 重置当前绘制格子
          }
        },

        // 重置词语状态 - 切换到新词语时调用
        resetWordState() {
          this.charResults = [];
          this.recognitionProgress = {};
          this.debugImages = [];
          this.clearCanvas();
        },

        // 清除画布
        clearCanvas() {
          this.drawTianziGrid(); // 清除后重新绘制田字格
          this.strokeHistory = []; // 清空撤销历史
          this.currentStroke = [];
          this.recognizedText = '';
          this.feedback = null;
        },

        // 撤销上一笔绘制
        undoLastStroke() {
          if (this.strokeHistory.length === 0) return;

          // 恢复到上一个状态
          const lastState = this.strokeHistory.pop();
          this.ctx.putImageData(lastState, 0, 0);

          this.recognizedText = '';
          this.feedback = null;
        },

        // 获取字符状态的CSS类名
        getCharStatusClass(index) {
          if (this.recognitionProgress[index] === 'recognizing') return 'current';
          if (this.charResults[index]) {
            return this.charResults[index].isCorrect ? 'correct' : 'incorrect';
          }
          return 'pending';
        },

        // 获取字符状态的显示文本
        getCharStatusText(index) {
          if (this.recognitionProgress[index] === 'recognizing') return '识别中';
          if (this.charResults[index]) {
            return this.charResults[index].isCorrect ? '正确' : '错误';
          }
          return '待识别';
        },

        // 显示调试图像 - 提取每个字符的图像用于调试
        showDebugImages() {
          this.debugImages = [];

          // 先在原canvas上绘制提取区域边界（用于调试）
          this.drawExtractionBounds();

          for (let i = 0; i < this.currentWord.text.length; i++) {
            const charCanvas = this.extractCharCanvasAtIndex(i);

            // 保存图像数据
            this.debugImages.push({
              canvas: charCanvas,
              width: charCanvas.width,
              height: charCanvas.height,
              imageData: charCanvas.getContext('2d').getImageData(0, 0, charCanvas.width, charCanvas.height)
            });
          }

          // 等待Vue更新DOM后绘制调试图像
          this.$nextTick(() => {
            this.renderDebugImages();
          });
        },

        // 绘制提取区域边界 - 用于调试
        drawExtractionBounds() {
          this.ctx.save();
          this.ctx.strokeStyle = 'red';
          this.ctx.lineWidth = 2;
          this.ctx.setLineDash([5, 5]); // 虚线样式

          for (let i = 0; i < this.currentWord.text.length; i++) {
            const x = i * (this.gridSize + this.spacing);
            this.ctx.strokeRect(x, 0, this.gridSize, this.gridSize);
          }

          this.ctx.restore();

          // 2秒后清除边界线
          setTimeout(() => {
            this.drawTianziGrid();
          }, 2000);
        },

        // 渲染调试图像到调试面板
        renderDebugImages() {
          this.debugImages.forEach((debugImg, index) => {
            const canvasRef = this.$refs[`debugCanvas${index}`];
            if (canvasRef && canvasRef[0]) {
              const canvas = canvasRef[0];
              const ctx = canvas.getContext('2d');

              // 清除画布
              ctx.clearRect(0, 0, canvas.width, canvas.height);

              // 绘制提取的图像
              ctx.putImageData(debugImg.imageData, 0, 0);
            }
          });
        },

        // 识别所有字符 - 主要的OCR识别函数
        async recognizeAllChars() {
          if (this.isRecognizing) return;

          this.isRecognizing = true;
          this.recognizedText = '';
          this.feedback = null;
          this.charResults = [];
          this.recognitionProgress = {};

          // 先显示调试图像
          this.showDebugImages();

          try {
            // 顺序识别每个字符（一个一个提交OCR）
            for (let i = 0; i < this.currentWord.text.length; i++) {
              this.recognitionProgress[i] = 'recognizing';

              try {
                console.log(`开始识别第${i + 1}个字符: ${this.currentWord.text[i]}`);
                const result = await this.recognizeCharAtIndex(i);
                this.charResults[i] = result;
                this.recognitionProgress[i] = 'completed';
                console.log(`第${i + 1}个字符识别完成:`, result);

                // 短暂延迟，让用户看到进度
                await new Promise(resolve => setTimeout(resolve, 500));

              } catch (error) {
                console.error(`第${i + 1}个字符识别失败:`, error);
                this.charResults[i] = {
                  recognized: '',
                  expected: this.currentWord.text[i],
                  isCorrect: false
                };
                this.recognitionProgress[i] = 'completed';
              }
            }

            // 处理最终结果
            const correctCount = this.charResults.filter(r => r.isCorrect).length;
            const totalCount = this.charResults.length;
            const allCorrect = correctCount === totalCount;

            let resultMessage = '';
            if (allCorrect) {
              resultMessage = `全部正确！识别结果：${this.charResults.map(r => r.recognized).join('')}`;
            } else {
              resultMessage = `识别完成：${correctCount}/${totalCount} 正确`;
              // 显示详细结果
              const details = this.charResults.map((r, i) =>
                `${this.currentWord.text[i]}→${r.recognized}${r.isCorrect ? '✓' : '✗'}`
              ).join(' ');
              resultMessage += `\n详情：${details}`;
            }

            this.feedback = {
              isCorrect: allCorrect,
              message: resultMessage
            };

            if (allCorrect) {
              setTimeout(() => {
                this.feedback = {
                  isCorrect: true,
                  message: '恭喜！可以进入下一个词语了'
                };
              }, 2000);
            }

          } catch (error) {
            console.error('识别失败:', error);
            this.feedback = {
              isCorrect: false,
              message: '识别失败，请重试'
            };
          } finally {
            this.isRecognizing = false;
          }
        },

        // 识别指定索引位置的字符
        async recognizeCharAtIndex(index) {
          try {
            // 创建只包含指定格子的canvas
            const charCanvas = this.extractCharCanvasAtIndex(index);

            const result = await Tesseract.recognize(
              charCanvas,
              'chi_sim', // 简体中文
              {
                logger: (m) => {
                  if (m.status === 'recognizing text') {
                    console.log(`字符${index + 1}识别进度: ${Math.round(m.progress * 100)}%`);
                  }
                },
                tessedit_pageseg_mode: Tesseract.PSM.SINGLE_CHAR, // 单字符模式
                tessedit_char_whitelist: this.currentWord.text, // 字符白名单
                preserve_interword_spaces: '0'
              }
            );

            let recognizedText = result.data.text.trim();

            // 清理识别结果，只保留中文
            recognizedText = recognizedText.replace(/[^\u4e00-\u9fa5]/g, '');

            // 检查字符
            const expectedChar = this.currentWord.text[index];
            const isCorrect = recognizedText === expectedChar;

            return {
              recognized: recognizedText,
              expected: expectedChar,
              isCorrect: isCorrect
            };

          } catch (error) {
            console.error(`字符${index + 1}识别失败:`, error);
            throw error;
          }
        },

        // 提取指定索引位置的字符画布
        extractCharCanvasAtIndex(index) {
          // 创建临时canvas，只包含指定字符格子的内容
          const tempCanvas = document.createElement('canvas');
          const tempCtx = tempCanvas.getContext('2d');

          // 设置canvas大小为单个格子大小
          tempCanvas.width = this.gridSize;
          tempCanvas.height = this.gridSize;

          // 填充白色背景
          tempCtx.fillStyle = 'white';
          tempCtx.fillRect(0, 0, this.gridSize, this.gridSize);

          // 计算指定格子在原canvas中的位置
          const sourceX = index * (this.gridSize + this.spacing);

          console.log(`提取字符${index + 1}:`, {
            sourceX: sourceX,
            sourceY: 0,
            width: this.gridSize,
            height: this.gridSize,
            canvasWidth: this.$refs.canvas.width,
            canvasHeight: this.$refs.canvas.height,
            displayWidth: this.$refs.canvas.style.width,
            displayHeight: this.$refs.canvas.style.height
          });

          // 需要考虑高DPI缩放
          const dpr = window.devicePixelRatio || 1;
          const actualSourceX = sourceX * dpr;
          const actualSourceY = 0;
          const actualWidth = this.gridSize * dpr;
          const actualHeight = this.gridSize * dpr;

          // 从原canvas中提取指定格子的内容
          tempCtx.drawImage(
            this.$refs.canvas,
            actualSourceX, actualSourceY, actualWidth, actualHeight,  // 源区域（考虑DPI）
            0, 0, this.gridSize, this.gridSize                        // 目标区域
          );

          return tempCanvas;
        },

        // 切换到下一个词语
        nextWord() {
          this.currentIndex = (this.currentIndex + 1) % this.words.length;
        }
      } 
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>
