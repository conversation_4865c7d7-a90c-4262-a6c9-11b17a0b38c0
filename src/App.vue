<template>
  <!-- Vue应用根容器 -->
  <div class="app-container">
    <!-- 应用标题 -->
    <h1 class="title">中文词语手写测试</h1>
    
    <!-- 词语信息显示区域 -->
    <div class="word-info">
      <!-- 显示当前词语的拼音 -->
      <p class="pinyin">拼音: {{ currentWord.pinyin }}</p>
      <!-- 播放发音按钮 -->
      <button @click="playPronunciation" class="play-btn">
        播放发音
      </button>
    </div>
    
    <!-- 字符状态显示区域 - 显示每个字符的识别状态 -->
    <div class="grid-status">
      <div 
        v-for="(char, index) in currentWord.text" 
        :key="index"
        class="char-status"
        :class="getCharStatusClass(index)"
      >
        {{ char }}
        <div style="font-size: 12px; margin-top: 2px;">
          {{ getCharStatusText(index) }}
        </div>
      </div>
    </div>

    <!-- 画布容器 - 包含手写区域 -->
    <div class="canvas-container" :class="{ loading: isRecognizing }">
      <!-- 手写画布 - 用户在此区域手写汉字 -->
      <canvas
        ref="canvas"
        :width="canvasWidth"
        :height="canvasHeight"
        class="writing-canvas"
        @mousedown="startDrawing"
        @mousemove="draw"
        @mouseup="stopDrawing"
        @mouseleave="stopDrawing"
        @touchstart.prevent="startDrawing"
        @touchmove.prevent="draw"
        @touchend.prevent="stopDrawing"
        @touchcancel.prevent="stopDrawing"
      ></canvas>
    </div>
    
    <!-- 主要操作按钮组 -->
    <div class="button-group">
      <!-- 清除画布按钮 -->
      <button
        @click="clearCanvas"
        :disabled="isRecognizing"
        class="btn btn-clear"
      >
        清除画布
      </button>
      
      <!-- 撤销上一笔按钮 -->
      <button
        @click="undoLastStroke"
        :disabled="isRecognizing || strokeHistory.length === 0"
        class="btn btn-undo"
      >
        撤销
      </button>
      
      <!-- 查看提取图像按钮 - 用于调试 -->
      <button
        @click="showDebugImages"
        :disabled="isRecognizing"
        class="btn btn-debug"
      >
        查看提取图像
      </button>
      
      <!-- 提交识别按钮 -->
      <button
        @click="recognizeAllChars"
        :disabled="isRecognizing"
        class="btn btn-submit"
      >
        <div class="btn-content">
          <span v-if="!isRecognizing">提交识别</span>
          <span v-else>
            <div class="spinner"></div>
            识别中...
          </span>
        </div>
      </button>
    </div>
    
    <!-- 下一个词语按钮 - 当所有字符都识别正确时显示 -->
    <div class="button-group" v-if="allCharsCompleted">
      <button
        @click="nextWord"
        class="btn btn-check"
        style="width: 100%;"
      >
        下一个词语
      </button>
    </div>

    <!-- 识别状态显示区域 -->
    <div class="recognition-status">
      <p v-if="feedback" class="feedback" :class="feedback.isCorrect ? 'correct' : 'incorrect'">
        {{ feedback.message }}
      </p>
    </div>
    
    <!-- 识别结果显示 -->
    <p v-if="recognizedText && !isRecognizing" class="recognized-text">
      识别结果: {{ recognizedText }}
    </p>
    
    <!-- 调试面板 - 显示提取的字符图像 -->
    <div v-if="debugImages.length > 0" class="debug-panel">
      <div class="debug-title">提取的字符图像（用于OCR识别）</div>
      <div class="debug-images">
        <div v-for="(debugImg, index) in debugImages" :key="index" class="debug-char">
          <!-- 显示提取的字符图像 -->
          <canvas
            :data-ref="`debugCanvas${index}`"
            :width="debugImg.width"
            :height="debugImg.height"
            style="width: 80px; height: 80px;"
          ></canvas>
          <!-- 显示字符信息和识别结果 -->
          <div class="debug-char-label">
            字符{{ index + 1 }}: {{ currentWord.text[index] }}
            <br>
            <span v-if="charResults[index]" :style="{ color: charResults[index].isCorrect ? '#28a745' : '#dc3545' }">
              识别: {{ charResults[index].recognized || '无' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 引入Vue 3 Composition API
import { ref, computed, onMounted, watch, nextTick } from 'vue'

// 引入Tesseract.js OCR库
import Tesseract from 'tesseract.js'

// ==================== 响应式数据定义 ====================

// 词语库 - 包含要练习的中文词语及其拼音和发音
const words = ref([
  { text: '你好', pinyin: 'nǐ hǎo', audio: 'https://dict.youdao.com/dictvoice?audio=nihao&type=1' },
  { text: '谢谢', pinyin: 'xiè xie', audio: 'https://dict.youdao.com/dictvoice?audio=xiexie&type=1' },
  { text: '再见', pinyin: 'zài jiàn', audio: 'https://dict.youdao.com/dictvoice?audio=zaijian&type=1' },
  { text: '朋友', pinyin: 'péng yǒu', audio: 'https://dict.youdao.com/dictvoice?audio=pengyou&type=1' },
  { text: '家人', pinyin: 'jiā rén', audio: 'https://dict.youdao.com/dictvoice?audio=jiaren&type=1' },
  { text: '学习', pinyin: 'xué xí', audio: 'https://dict.youdao.com/dictvoice?audio=xuexi&type=1' },
  { text: '工作', pinyin: 'gōng zuò', audio: 'https://dict.youdao.com/dictvoice?audio=gongzuo&type=1' },
  { text: '时间', pinyin: 'shí jiān', audio: 'https://dict.youdao.com/dictvoice?audio=shijian&type=1' }
])

// 基础状态数据
const currentIndex = ref(0) // 当前词语的索引
const recognizedText = ref('') // OCR识别的文本结果
const feedback = ref(null) // 反馈信息对象
const isDrawing = ref(false) // 是否正在绘制
const isRecognizing = ref(false) // 是否正在进行OCR识别

// Canvas相关数据
const canvas = ref(null) // Canvas DOM引用
let ctx = null // Canvas 2D绘图上下文
const gridSize = ref(120) // 每个田字格的大小（像素）
const spacing = ref(15) // 田字格之间的间距（像素）
const lastX = ref(0) // 上一个绘制点的X坐标
const lastY = ref(0) // 上一个绘制点的Y坐标

// 绘制历史和状态
const strokeHistory = ref([]) // 存储每一笔的画布状态，用于撤销功能
const currentStroke = ref([]) // 当前正在绘制的笔画点集合
const charResults = ref([]) // 每个字符的识别结果数组
const recognitionProgress = ref({}) // 识别进度状态对象
const debugImages = ref([]) // 调试用的提取图像数组
const currentDrawingGrid = ref(-1) // 当前正在绘制的格子索引

// ==================== 计算属性 ====================

// 获取当前词语对象
const currentWord = computed(() => {
  return words.value[currentIndex.value]
})

// 计算画布宽度 - 根据字符数量、格子大小和间距计算
const canvasWidth = computed(() => {
  return currentWord.value.text.length * gridSize.value + (currentWord.value.text.length - 1) * spacing.value
})

// 计算画布高度 - 等于单个格子的高度
const canvasHeight = computed(() => {
  return gridSize.value
})

// 检查是否所有字符都识别正确
const allCharsCompleted = computed(() => {
  return charResults.value.length === currentWord.value.text.length &&
         charResults.value.every(result => result && result.isCorrect)
})

// ==================== 方法定义 ====================

// 设置高DPI支持 - 确保在高分辨率屏幕上显示清晰
const setupHighDPI = () => {
  const canvasEl = canvas.value
  const dpr = window.devicePixelRatio || 1 // 获取设备像素比

  // 设置画布的实际像素大小
  canvasEl.width = canvasWidth.value * dpr
  canvasEl.height = canvasHeight.value * dpr

  // 设置画布的CSS显示大小
  canvasEl.style.width = canvasWidth.value + 'px'
  canvasEl.style.height = canvasHeight.value + 'px'

  // 缩放绘图上下文以匹配设备像素比
  ctx.scale(dpr, dpr)
}

// 绘制田字格背景
const drawTianziGrid = () => {
  // 清除整个画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

  // 保存当前绘图状态
  ctx.save()

  // 为每个字符绘制田字格
  for (let i = 0; i < currentWord.value.text.length; i++) {
    const x = i * (gridSize.value + spacing.value) // 计算当前格子的X坐标

    // 绘制外框 - 使用较粗的线条
    ctx.strokeStyle = '#999999'
    ctx.lineWidth = 2
    ctx.strokeRect(x, 0, gridSize.value, gridSize.value)

    // 绘制内部辅助线 - 使用细线
    ctx.strokeStyle = '#dddddd'
    ctx.lineWidth = 1

    // 绘制垂直中线
    ctx.beginPath()
    ctx.moveTo(x + gridSize.value / 2, 0)
    ctx.lineTo(x + gridSize.value / 2, gridSize.value)
    ctx.stroke()

    // 绘制水平中线
    ctx.beginPath()
    ctx.moveTo(x, gridSize.value / 2)
    ctx.lineTo(x + gridSize.value, gridSize.value / 2)
    ctx.stroke()

    // 绘制对角线 - 使用更淡的颜色
    ctx.strokeStyle = '#eeeeee'
    ctx.lineWidth = 0.5

    // 左上到右下对角线
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x + gridSize.value, gridSize.value)
    ctx.stroke()

    // 右上到左下对角线
    ctx.beginPath()
    ctx.moveTo(x + gridSize.value, 0)
    ctx.lineTo(x, gridSize.value)
    ctx.stroke()
  }

  // 恢复绘图状态，为手写做准备
  ctx.restore()
  ctx.lineWidth = 3
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  ctx.strokeStyle = '#000000'
  ctx.fillStyle = '#000000'
}

// 保存当前画布状态到历史记录（用于撤销功能）
const saveCanvasState = () => {
  const imageData = ctx.getImageData(0, 0, canvas.value.width, canvas.value.height)
  strokeHistory.value.push(imageData)

  // 限制历史记录数量，避免内存过多占用
  if (strokeHistory.value.length > 20) {
    strokeHistory.value.shift() // 删除最早的记录
  }
}

// 播放当前词语的发音
const playPronunciation = () => {
  const audio = new Audio(currentWord.value.audio)
  audio.play().catch(error => {
    console.error('播放发音失败:', error)
  })
}

// 获取事件在画布中的坐标位置
const getEventPos = (e) => {
  const rect = canvas.value.getBoundingClientRect()
  const clientX = e.clientX || (e.touches && e.touches[0].clientX)
  const clientY = e.clientY || (e.touches && e.touches[0].clientY)
  return {
    x: (clientX - rect.left) * (canvas.value.width / rect.width) / (window.devicePixelRatio || 1),
    y: (clientY - rect.top) * (canvas.value.height / rect.height) / (window.devicePixelRatio || 1)
  }
}

// 获取坐标所在的格子索引
const getGridIndex = (x, y) => {
  for (let i = 0; i < currentWord.value.text.length; i++) {
    const gridX = i * (gridSize.value + spacing.value)
    if (x >= gridX && x <= gridX + gridSize.value && y >= 0 && y <= gridSize.value) {
      return i // 返回格子索引
    }
  }
  return -1 // 不在任何格子内
}



// 开始绘制 - 鼠标按下或触摸开始时调用
const startDrawing = (e) => {
  if (isRecognizing.value) return // 识别中时禁止绘制

  const pos = getEventPos(e)

  // 检查是否在任何格子内
  const gridIndex = getGridIndex(pos.x, pos.y)
  if (gridIndex === -1) return // 不在格子内则不开始绘制

  // 记录当前绘制的格子索引
  currentDrawingGrid.value = gridIndex

  // 在开始新的笔画前保存当前状态
  saveCanvasState()

  isDrawing.value = true
  lastX.value = pos.x
  lastY.value = pos.y
  currentStroke.value = [pos]
  ctx.beginPath()
  ctx.moveTo(pos.x, pos.y)
}

// 绘制过程 - 鼠标移动或触摸移动时调用
const draw = (e) => {
  if (!isDrawing.value || isRecognizing.value) return
  const pos = getEventPos(e)

  // 检查是否还在同一个格子内
  const gridIndex = getGridIndex(pos.x, pos.y)
  if (gridIndex !== currentDrawingGrid.value) {
    stopDrawing() // 离开当前格子则停止绘制
    return
  }

  // 记录当前笔画的点
  currentStroke.value.push(pos)

  // 使用二次贝塞尔曲线使线条更平滑
  ctx.quadraticCurveTo(lastX.value, lastY.value, (pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2)
  ctx.stroke()
  ctx.beginPath()
  ctx.moveTo((pos.x + lastX.value) / 2, (pos.y + lastY.value) / 2)

  lastX.value = pos.x
  lastY.value = pos.y
}

// 停止绘制 - 鼠标抬起或触摸结束时调用
const stopDrawing = () => {
  if (isDrawing.value) {
    isDrawing.value = false
    ctx.closePath()
    currentStroke.value = [] // 清空当前笔画
    currentDrawingGrid.value = -1 // 重置当前绘制格子
  }
}

// 重置词语状态 - 切换到新词语时调用
const resetWordState = () => {
  charResults.value = []
  recognitionProgress.value = {}
  debugImages.value = []
  clearCanvas()
}

// 清除画布
const clearCanvas = () => {
  drawTianziGrid() // 清除后重新绘制田字格
  strokeHistory.value = [] // 清空撤销历史
  currentStroke.value = []
  recognizedText.value = ''
  feedback.value = null
}

// 撤销上一笔绘制
const undoLastStroke = () => {
  if (strokeHistory.value.length === 0) return

  // 恢复到上一个状态
  const lastState = strokeHistory.value.pop()
  ctx.putImageData(lastState, 0, 0)

  recognizedText.value = ''
  feedback.value = null
}

// 获取字符状态的CSS类名
const getCharStatusClass = (index) => {
  if (recognitionProgress.value[index] === 'recognizing') return 'current'
  if (charResults.value[index]) {
    return charResults.value[index].isCorrect ? 'correct' : 'incorrect'
  }
  return 'pending'
}

// 获取字符状态的显示文本
const getCharStatusText = (index) => {
  if (recognitionProgress.value[index] === 'recognizing') return '识别中'
  if (charResults.value[index]) {
    return charResults.value[index].isCorrect ? '正确' : '错误'
  }
  return '待识别'
}

// 显示调试图像 - 提取每个字符的图像用于调试
const showDebugImages = () => {
  debugImages.value = []

  // 先在原canvas上绘制提取区域边界（用于调试）
  drawExtractionBounds()

  for (let i = 0; i < currentWord.value.text.length; i++) {
    const charCanvas = extractCharCanvasAtIndex(i)

    // 保存图像数据
    debugImages.value.push({
      canvas: charCanvas,
      width: charCanvas.width,
      height: charCanvas.height,
      imageData: charCanvas.getContext('2d').getImageData(0, 0, charCanvas.width, charCanvas.height)
    })
  }

  // 等待Vue更新DOM后绘制调试图像
  nextTick(() => {
    renderDebugImages()
  })
}

// 绘制提取区域边界 - 用于调试
const drawExtractionBounds = () => {
  ctx.save()
  ctx.strokeStyle = 'red'
  ctx.lineWidth = 2
  ctx.setLineDash([5, 5]) // 虚线样式

  for (let i = 0; i < currentWord.value.text.length; i++) {
    const x = i * (gridSize.value + spacing.value)
    ctx.strokeRect(x, 0, gridSize.value, gridSize.value)
  }

  ctx.restore()

  // 2秒后清除边界线
  setTimeout(() => {
    drawTianziGrid()
  }, 2000)
}

// 渲染调试图像到调试面板
const renderDebugImages = () => {
  debugImages.value.forEach((debugImg, index) => {
    const canvasRef = document.querySelector(`[data-ref="debugCanvas${index}"]`)
    if (canvasRef) {
      const ctx = canvasRef.getContext('2d')

      // 清除画布
      ctx.clearRect(0, 0, canvasRef.width, canvasRef.height)

      // 绘制提取的图像
      ctx.putImageData(debugImg.imageData, 0, 0)
    }
  })
}

// 提取指定索引位置的字符画布
const extractCharCanvasAtIndex = (index) => {
  // 创建临时canvas，只包含指定字符格子的内容
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')

  // 设置canvas大小为单个格子大小
  tempCanvas.width = gridSize.value
  tempCanvas.height = gridSize.value

  // 填充白色背景
  tempCtx.fillStyle = 'white'
  tempCtx.fillRect(0, 0, gridSize.value, gridSize.value)

  // 计算指定格子在原canvas中的位置
  const sourceX = index * (gridSize.value + spacing.value)

  console.log(`提取字符${index + 1}:`, {
    sourceX: sourceX,
    sourceY: 0,
    width: gridSize.value,
    height: gridSize.value,
    canvasWidth: canvas.value.width,
    canvasHeight: canvas.value.height,
    displayWidth: canvas.value.style.width,
    displayHeight: canvas.value.style.height
  })

  // 需要考虑高DPI缩放
  const dpr = window.devicePixelRatio || 1
  const actualSourceX = sourceX * dpr
  const actualSourceY = 0
  const actualWidth = gridSize.value * dpr
  const actualHeight = gridSize.value * dpr

  // 从原canvas中提取指定格子的内容
  tempCtx.drawImage(
    canvas.value,
    actualSourceX, actualSourceY, actualWidth, actualHeight,  // 源区域（考虑DPI）
    0, 0, gridSize.value, gridSize.value                        // 目标区域
  )

  return tempCanvas
}

// 识别所有字符 - 主要的OCR识别函数
const recognizeAllChars = async () => {
  if (isRecognizing.value) return

  isRecognizing.value = true
  recognizedText.value = ''
  feedback.value = null
  charResults.value = []
  recognitionProgress.value = {}

  // 先显示调试图像
  showDebugImages()

  try {
    // 顺序识别每个字符（一个一个提交OCR）
    for (let i = 0; i < currentWord.value.text.length; i++) {
      recognitionProgress.value[i] = 'recognizing'

      try {
        console.log(`开始识别第${i + 1}个字符: ${currentWord.value.text[i]}`)
        const result = await recognizeCharAtIndex(i)
        charResults.value[i] = result
        recognitionProgress.value[i] = 'completed'
        console.log(`第${i + 1}个字符识别完成:`, result)

        // 短暂延迟，让用户看到进度
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (error) {
        console.error(`第${i + 1}个字符识别失败:`, error)
        charResults.value[i] = {
          recognized: '',
          expected: currentWord.value.text[i],
          isCorrect: false
        }
        recognitionProgress.value[i] = 'completed'
      }
    }

    // 处理最终结果
    const correctCount = charResults.value.filter(r => r.isCorrect).length
    const totalCount = charResults.value.length
    const allCorrect = correctCount === totalCount

    let resultMessage = ''
    if (allCorrect) {
      resultMessage = `全部正确！识别结果：${charResults.value.map(r => r.recognized).join('')}`
    } else {
      resultMessage = `识别完成：${correctCount}/${totalCount} 正确`
      // 显示详细结果
      const details = charResults.value.map((r, i) =>
        `${currentWord.value.text[i]}→${r.recognized}${r.isCorrect ? '✓' : '✗'}`
      ).join(' ')
      resultMessage += `\n详情：${details}`
    }

    feedback.value = {
      isCorrect: allCorrect,
      message: resultMessage
    }

    if (allCorrect) {
      setTimeout(() => {
        feedback.value = {
          isCorrect: true,
          message: '恭喜！可以进入下一个词语了'
        }
      }, 2000)
    }

  } catch (error) {
    console.error('识别失败:', error)
    feedback.value = {
      isCorrect: false,
      message: '识别失败，请重试'
    }
  } finally {
    isRecognizing.value = false
  }
}

// 识别指定索引位置的字符
const recognizeCharAtIndex = async (index) => {
  try {
    // 创建只包含指定格子的canvas
    const charCanvas = extractCharCanvasAtIndex(index)

    const result = await Tesseract.recognize(
      charCanvas,
      'chi_sim', // 简体中文
      {
        logger: (m) => {
          if (m.status === 'recognizing text') {
            console.log(`字符${index + 1}识别进度: ${Math.round(m.progress * 100)}%`)
          }
        },
        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_CHAR, // 单字符模式
        tessedit_char_whitelist: currentWord.value.text, // 字符白名单
        preserve_interword_spaces: '0'
      }
    )

    let recognizedText = result.data.text.trim()

    // 清理识别结果，只保留中文
    recognizedText = recognizedText.replace(/[^\u4e00-\u9fa5]/g, '')

    // 检查字符
    const expectedChar = currentWord.value.text[index]
    const isCorrect = recognizedText === expectedChar

    return {
      recognized: recognizedText,
      expected: expectedChar,
      isCorrect: isCorrect
    }

  } catch (error) {
    console.error(`字符${index + 1}识别失败:`, error)
    throw error
  }
}

// 切换到下一个词语
const nextWord = () => {
  currentIndex.value = (currentIndex.value + 1) % words.value.length
}

// ==================== 生命周期钩子 ====================

// 组件挂载后的钩子函数
onMounted(() => {
  // 获取画布的2D绘图上下文
  ctx = canvas.value.getContext('2d')

  // 设置画笔属性
  ctx.lineWidth = 3 // 线条宽度
  ctx.lineCap = 'round' // 线条端点样式为圆形
  ctx.lineJoin = 'round' // 线条连接点样式为圆形
  ctx.strokeStyle = '#000000' // 线条颜色为黑色
  ctx.fillStyle = '#000000' // 填充颜色为黑色

  // 设置高DPI支持，确保在高分辨率屏幕上显示清晰
  setupHighDPI()

  // 绘制田字格背景
  drawTianziGrid()
})

// ==================== 监听器 ====================

// 当前词语改变时重置状态
watch(currentWord, () => {
  resetWordState()
})
</script>

<style>
/* 全局样式重置 - 清除默认边距和内边距 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box; /* 使用边框盒模型 */
}

/* 页面主体样式 */
body {
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif; /* 中文友好字体 */
  background-color: #f5f5f5; /* 浅灰色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh; /* 最小高度为视窗高度 */
  padding: 20px;
}

/* 应用主容器样式 */
.app-container {
  background: white;
  padding: 30px;
  border-radius: 12px; /* 圆角边框 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  width: 100%;
  max-width: 600px; /* 最大宽度限制 */
}

/* 标题样式 */
.title {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

/* 词语信息区域样式 */
.word-info {
  margin-bottom: 25px;
  text-align: center;
}

/* 拼音显示样式 */
.pinyin {
  font-size: 20px;
  color: #666;
  margin-bottom: 15px;
}

/* 播放发音按钮样式 */
.play-btn {
  background: #007bff; /* 蓝色背景 */
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s; /* 背景色过渡动画 */
}

/* 播放按钮悬停效果 */
.play-btn:hover {
  background: #0056b3; /* 深蓝色 */
}

/* 画布容器样式 */
.canvas-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin: 25px 0;
  background: #fafafa; /* 浅灰色背景 */
  border-radius: 8px;
  padding: 20px;
}

/* 手写画布样式 */
.writing-canvas {
  background: white;
  cursor: crosshair; /* 十字光标 */
  border-radius: 4px;
}

/* 加载状态样式 - 识别时的半透明效果 */
.loading {
  opacity: 0.6;
  pointer-events: none; /* 禁用鼠标事件 */
}

/* 按钮组容器样式 */
.button-group {
  display: flex;
  justify-content: space-between;
  gap: 15px; /* 按钮间距 */
  margin-bottom: 25px;
}

/* 通用按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s; /* 所有属性过渡动画 */
  flex: 1; /* 等宽分布 */
}

/* 禁用状态按钮样式 */
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 清除按钮样式 */
.btn-clear {
  background: #6c757d; /* 灰色 */
  color: white;
}

.btn-clear:hover:not(:disabled) {
  background: #545b62; /* 深灰色 */
}

/* 提交按钮样式 */
.btn-submit {
  background: #28a745; /* 绿色 */
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #1e7e34; /* 深绿色 */
}

/* 撤销按钮样式 */
.btn-undo {
  background: #ffc107; /* 黄色 */
  color: #212529; /* 深色文字 */
}

.btn-undo:hover:not(:disabled) {
  background: #e0a800; /* 深黄色 */
}

/* 检查按钮样式 */
.btn-check {
  background: #17a2b8; /* 青色 */
  color: white;
}

.btn-check:hover:not(:disabled) {
  background: #138496; /* 深青色 */
}

/* 字符状态显示区域样式 */
.grid-status {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 15px 0;
  flex-wrap: wrap; /* 允许换行 */
}

/* 单个字符状态样式 */
.char-status {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
}

/* 待识别状态样式 */
.char-status.pending {
  background: #f8f9fa;
  color: #6c757d;
  border: 2px solid #dee2e6;
}

/* 识别正确状态样式 */
.char-status.correct {
  background: #d4edda;
  color: #155724;
  border: 2px solid #c3e6cb;
}

/* 识别错误状态样式 */
.char-status.incorrect {
  background: #f8d7da;
  color: #721c24;
  border: 2px solid #f5c6cb;
}

/* 当前识别中状态样式 */
.char-status.current {
  background: #fff3cd;
  color: #856404;
  border: 2px solid #ffeaa7;
}

/* 调试面板样式 */
.debug-panel {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

/* 调试标题样式 */
.debug-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #495057;
}

/* 调试图像容器样式 */
.debug-images {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

/* 单个调试字符样式 */
.debug-char {
  text-align: center;
}

/* 调试画布样式 */
.debug-char canvas {
  border: 1px solid #ccc;
  background: white;
}

/* 调试字符标签样式 */
.debug-char-label {
  font-size: 12px;
  margin-top: 5px;
  color: #666;
}

/* 调试按钮样式 */
.btn-debug {
  background: #6f42c1; /* 紫色 */
  color: white;
}

.btn-debug:hover:not(:disabled) {
  background: #5a32a3; /* 深紫色 */
}

/* 按钮内容容器样式 */
.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载动画样式 */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  animation: spin 1s linear infinite; /* 旋转动画 */
  margin-right: 8px;
}

/* 旋转动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 识别状态显示区域样式 */
.recognition-status {
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

/* 反馈信息样式 */
.feedback {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

/* 正确反馈样式 */
.feedback.correct {
  color: #28a745;
}

/* 错误反馈样式 */
.feedback.incorrect {
  color: #dc3545;
}

/* 识别结果文本样式 */
.recognized-text {
  text-align: center;
  color: #666;
  font-size: 16px;
  margin-top: 10px;
}
</style>
